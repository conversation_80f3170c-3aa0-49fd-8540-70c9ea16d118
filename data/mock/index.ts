// 模拟数据中心
export interface Doctor {
  id: string;
  name: string;
  title: string;
  department: string;
  avatar: string;
  rating: number;
  experience: string;
  specialty: string[];
  availableSlots: TimeSlot[];
  fee: number;
}

export interface TimeSlot {
  date: string;
  period: 'morning' | 'afternoon' | 'evening';
  time: string;
  available: boolean;
}

export interface Department {
  id: string;
  name: string;
  icon: string;
  description: string;
  doctors: string[]; // doctor ids
}

export interface Appointment {
  id: string;
  doctorId: string;
  doctorName: string;
  department: string;
  date: string;
  time: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  symptoms: string;
  fee: number;
  location: string;
}

export interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: number;
  options?: string[]; // AI消息可能包含选项按钮
}

// 科室数据
export const departments: Department[] = [
  {
    id: '1',
    name: '内科',
    icon: 'https://img.icons8.com/color/48/stethoscope.png',
    description: '感冒发烧、消化不良、高血压等',
    doctors: ['1', '2']
  },
  {
    id: '2', 
    name: '外科',
    icon: 'https://img.icons8.com/color/48/surgery.png',
    description: '外伤处理、手术治疗等',
    doctors: ['3']
  },
  {
    id: '3',
    name: '儿科',
    icon: 'https://img.icons8.com/color/48/baby.png', 
    description: '儿童疾病、疫苗接种等',
    doctors: ['4']
  },
  {
    id: '4',
    name: '妇科',
    icon: 'https://img.icons8.com/color/48/female.png',
    description: '妇科检查、孕期保健等', 
    doctors: ['5']
  },
  {
    id: '5',
    name: '骨科',
    icon: 'https://img.icons8.com/color/48/broken-bone.png',
    description: '骨折、关节疾病等',
    doctors: ['6']
  },
  {
    id: '6',
    name: '眼科',
    icon: 'https://img.icons8.com/color/48/eye.png',
    description: '视力检查、眼部疾病等',
    doctors: ['7']
  }
];

// 医生数据
export const doctors: Doctor[] = [
  {
    id: '1',
    name: '张主任',
    title: '主任医师',
    department: '内科',
    avatar: 'https://img.icons8.com/color/96/doctor-male.png',
    rating: 4.8,
    experience: '从医20年',
    specialty: ['呼吸道感染', '消化系统疾病', '高血压'],
    fee: 35,
    availableSlots: [
      { date: '2024-12-15', period: 'morning', time: '09:00-11:00', available: true },
      { date: '2024-12-15', period: 'afternoon', time: '14:00-16:00', available: false },
      { date: '2024-12-16', period: 'morning', time: '09:00-11:00', available: true }
    ]
  },
  {
    id: '2',
    name: '李医生',
    title: '副主任医师', 
    department: '内科',
    avatar: 'https://img.icons8.com/color/96/doctor-female.png',
    rating: 4.6,
    experience: '从医15年',
    specialty: ['心血管疾病', '糖尿病', '内分泌'],
    fee: 30,
    availableSlots: [
      { date: '2024-12-15', period: 'morning', time: '08:00-10:00', available: true },
      { date: '2024-12-15', period: 'afternoon', time: '15:00-17:00', available: true }
    ]
  },
  {
    id: '3',
    name: '王医生',
    title: '主治医师',
    department: '外科', 
    avatar: 'https://img.icons8.com/color/96/surgeon.png',
    rating: 4.7,
    experience: '从医12年',
    specialty: ['普通外科', '微创手术', '外伤处理'],
    fee: 40,
    availableSlots: [
      { date: '2024-12-15', period: 'morning', time: '10:00-12:00', available: true }
    ]
  },
  {
    id: '4',
    name: '陈医生',
    title: '主任医师',
    department: '儿科',
    avatar: 'https://img.icons8.com/color/96/pediatrician.png', 
    rating: 4.9,
    experience: '从医18年',
    specialty: ['儿童感冒', '疫苗接种', '生长发育'],
    fee: 45,
    availableSlots: [
      { date: '2024-12-15', period: 'afternoon', time: '14:00-16:00', available: true }
    ]
  },
  {
    id: '5',
    name: '刘医生',
    title: '副主任医师',
    department: '妇科',
    avatar: 'https://img.icons8.com/color/96/gynecology.png',
    rating: 4.5,
    experience: '从医16年', 
    specialty: ['妇科检查', '孕期保健', '妇科炎症'],
    fee: 38,
    availableSlots: [
      { date: '2024-12-16', period: 'morning', time: '09:00-11:00', available: true }
    ]
  },
  {
    id: '6',
    name: '赵医生',
    title: '主治医师',
    department: '骨科',
    avatar: 'https://img.icons8.com/color/96/orthopedist.png',
    rating: 4.4,
    experience: '从医10年',
    specialty: ['骨折治疗', '关节疾病', '运动损伤'],
    fee: 42,
    availableSlots: [
      { date: '2024-12-15', period: 'afternoon', time: '15:00-17:00', available: true }
    ]
  },
  {
    id: '7',
    name: '孙医生', 
    title: '主任医师',
    department: '眼科',
    avatar: 'https://img.icons8.com/color/96/ophthalmologist.png',
    rating: 4.8,
    experience: '从医22年',
    specialty: ['近视治疗', '白内障', '眼底疾病'],
    fee: 50,
    availableSlots: [
      { date: '2024-12-16', period: 'afternoon', time: '14:00-16:00', available: true }
    ]
  }
];

// AI问诊症状关键词映射
export const symptomDepartmentMap: Record<string, string[]> = {
  '发烧': ['内科'],
  '头疼': ['内科', '神经内科'],
  '咳嗽': ['内科', '呼吸科'],
  '胸痛': ['内科', '心内科'],
  '腹痛': ['内科', '消化科'],
  '外伤': ['外科'],
  '骨折': ['骨科'],
  '关节痛': ['骨科', '风湿科'],
  '视力模糊': ['眼科'],
  '眼痛': ['眼科'],
  '妇科检查': ['妇科'],
  '怀孕': ['妇科', '产科'],
  '儿童发烧': ['儿科'],
  '疫苗': ['儿科']
};

// 模拟预约数据
export const mockAppointments: Appointment[] = [
  {
    id: '1',
    doctorId: '1',
    doctorName: '张主任',
    department: '内科',
    date: '2024-12-15',
    time: '09:00',
    status: 'confirmed',
    symptoms: '发烧头疼',
    fee: 35,
    location: '门诊2楼内科'
  }
];
