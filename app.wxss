/**app.wxss**/
/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 主题色彩 */
.primary-color { color: #0052D9; }
.success-color { color: #00A870; }
.warning-color { color: #ED7B2F; }
.error-color { color: #E34D59; }
.text-primary { color: #000000; }
.text-secondary { color: #666666; }
.text-placeholder { color: #BBBBBB; }

/* 背景色 */
.bg-primary { background-color: #0052D9; }
.bg-success { background-color: #00A870; }
.bg-white { background-color: #ffffff; }
.bg-gray { background-color: #f5f5f5; }

/* 通用容器 */
.container {
  padding: 32rpx;
  background-color: #ffffff;
}

.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* AI问诊专用样式 */
.ai-gradient {
  background: linear-gradient(135deg, #0052D9 0%, #0085FF 100%);
}

.chat-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 卡片样式 */
.card-shadow {
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
}

/* 按钮样式增强 */
.btn-large {
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.btn-ai {
  background: linear-gradient(135deg, #0052D9 0%, #0085FF 100%);
  color: white;
  border: none;
}

/* 间距工具类 */
.mt-16 { margin-top: 16rpx; }
.mt-32 { margin-top: 32rpx; }
.mb-16 { margin-bottom: 16rpx; }
.mb-32 { margin-bottom: 32rpx; }
.ml-16 { margin-left: 16rpx; }
.mr-16 { margin-right: 16rpx; }

.pt-16 { padding-top: 16rpx; }
.pt-32 { padding-top: 32rpx; }
.pb-16 { padding-bottom: 16rpx; }
.pb-32 { padding-bottom: 32rpx; }
.pl-16 { padding-left: 16rpx; }
.pr-16 { padding-right: 16rpx; }

/* Flex布局 */
.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { justify-content: center; align-items: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }
.flex-1 { flex: 1; }

/* 文字样式 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: bold; }
.font-medium { font-weight: 500; }

.text-xs { font-size: 24rpx; }
.text-sm { font-size: 28rpx; }
.text-base { font-size: 32rpx; }
.text-lg { font-size: 36rpx; }
.text-xl { font-size: 40rpx; }
.text-2xl { font-size: 48rpx; }
