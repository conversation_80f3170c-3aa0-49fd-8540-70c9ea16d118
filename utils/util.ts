// 工具函数集合

// 时间格式化
export const formatTime = (date: Date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return (
    [year, month, day].map(formatNumber).join('/') +
    ' ' +
    [hour, minute, second].map(formatNumber).join(':')
  )
}

const formatNumber = (n: number) => {
  const s = n.toString()
  return s[1] ? s : '0' + s
}

// 格式化日期为中文
export const formatDateChinese = (dateStr: string) => {
  const date = new Date(dateStr);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
  const weekday = weekdays[date.getDay()];

  return `${month}月${day}日 周${weekday}`;
}

// 格式化时间段
export const formatTimePeriod = (period: 'morning' | 'afternoon' | 'evening') => {
  const periodMap = {
    morning: '上午',
    afternoon: '下午',
    evening: '晚上'
  };
  return periodMap[period] || period;
}

// 计算时间差
export const getTimeDiff = (targetTime: string) => {
  const now = new Date().getTime();
  const target = new Date(targetTime).getTime();
  const diff = target - now;

  if (diff <= 0) return '已过期';

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) return `${days}天后`;
  if (hours > 0) return `${hours}小时后`;
  if (minutes > 0) return `${minutes}分钟后`;
  return '即将开始';
}

// 防抖函数
export const debounce = (func: Function, wait: number) => {
  let timeout: any;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 节流函数
export const throttle = (func: Function, limit: number) => {
  let inThrottle: boolean;
  return function executedFunction(...args: any[]) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 生成唯一ID
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 深拷贝
export const deepClone = (obj: any): any => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
}

// 验证手机号
export const validatePhone = (phone: string) => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

// 验证身份证号
export const validateIdCard = (idCard: string) => {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return idCardRegex.test(idCard);
}

// 存储工具
export const storage = {
  set: (key: string, value: any) => {
    try {
      wx.setStorageSync(key, value);
      return true;
    } catch (error) {
      console.error('存储失败:', error);
      return false;
    }
  },

  get: (key: string, defaultValue: any = null) => {
    try {
      return wx.getStorageSync(key) || defaultValue;
    } catch (error) {
      console.error('读取存储失败:', error);
      return defaultValue;
    }
  },

  remove: (key: string) => {
    try {
      wx.removeStorageSync(key);
      return true;
    } catch (error) {
      console.error('删除存储失败:', error);
      return false;
    }
  },

  clear: () => {
    try {
      wx.clearStorageSync();
      return true;
    } catch (error) {
      console.error('清空存储失败:', error);
      return false;
    }
  }
};

// Toast提示工具
export const toast = {
  success: (title: string, duration: number = 2000) => {
    wx.showToast({
      title,
      icon: 'success',
      duration
    });
  },

  error: (title: string, duration: number = 2000) => {
    wx.showToast({
      title,
      icon: 'error',
      duration
    });
  },

  loading: (title: string = '加载中...') => {
    wx.showLoading({
      title,
      mask: true
    });
  },

  hide: () => {
    wx.hideToast();
    wx.hideLoading();
  }
};
