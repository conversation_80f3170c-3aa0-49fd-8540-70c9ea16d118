// API服务层 - 为后续真实API集成做准备
import { Appointment, Doctor, Department } from '../../data/mock/index';

// API配置
const API_CONFIG = {
  baseURL: 'https://api.hospital.com', // 替换为真实API地址
  timeout: 10000,
  version: 'v1'
};

// 请求拦截器
const request = (options: any) => {
  return new Promise((resolve, reject) => {
    // 添加通用请求头
    const header = {
      'Content-Type': 'application/json',
      'Authorization': wx.getStorageSync('token') || '',
      ...options.header
    };

    // 显示加载状态
    if (options.showLoading !== false) {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });
    }

    wx.request({
      url: `${API_CONFIG.baseURL}${options.url}`,
      method: options.method || 'GET',
      data: options.data,
      header,
      timeout: API_CONFIG.timeout,
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`));
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('API请求失败:', error);
        
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        
        reject(error);
      }
    });
  });
};

// API接口定义
export class ApiService {
  // 用户相关API
  static async login(code: string) {
    // 目前使用模拟登录
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          token: 'mock_token_' + Date.now(),
          userInfo: {
            id: '1',
            nickname: '用户',
            avatar: 'https://img.icons8.com/color/96/user.png'
          }
        });
      }, 1000);
    });
    
    // 真实API调用示例：
    // return request({
    //   url: '/auth/login',
    //   method: 'POST',
    //   data: { code }
    // });
  }

  // 科室相关API
  static async getDepartments(): Promise<Department[]> {
    // 目前返回模拟数据
    const { departments } = await import('../../data/mock/index');
    return new Promise((resolve) => {
      setTimeout(() => resolve(departments), 500);
    });
    
    // 真实API调用示例：
    // return request({
    //   url: '/departments',
    //   method: 'GET'
    // });
  }

  // 医生相关API
  static async getDoctors(departmentId?: string): Promise<Doctor[]> {
    // 目前返回模拟数据
    const { doctors } = await import('../../data/mock/index');
    return new Promise((resolve) => {
      setTimeout(() => {
        if (departmentId) {
          const filteredDoctors = doctors.filter(d => d.department === departmentId);
          resolve(filteredDoctors);
        } else {
          resolve(doctors);
        }
      }, 500);
    });
    
    // 真实API调用示例：
    // return request({
    //   url: '/doctors',
    //   method: 'GET',
    //   data: departmentId ? { departmentId } : {}
    // });
  }

  static async getDoctorById(doctorId: string): Promise<Doctor | null> {
    const { doctors } = await import('../../data/mock/index');
    return new Promise((resolve) => {
      setTimeout(() => {
        const doctor = doctors.find(d => d.id === doctorId);
        resolve(doctor || null);
      }, 300);
    });
    
    // 真实API调用示例：
    // return request({
    //   url: `/doctors/${doctorId}`,
    //   method: 'GET'
    // });
  }

  // 预约相关API
  static async getAppointments(): Promise<Appointment[]> {
    // 目前从本地存储获取
    return new Promise((resolve) => {
      setTimeout(() => {
        const appointments = wx.getStorageSync('appointments') || [];
        resolve(appointments);
      }, 500);
    });
    
    // 真实API调用示例：
    // return request({
    //   url: '/appointments',
    //   method: 'GET'
    // });
  }

  static async createAppointment(appointment: Omit<Appointment, 'id'>): Promise<Appointment> {
    // 目前保存到本地存储
    return new Promise((resolve) => {
      setTimeout(() => {
        const newAppointment = {
          ...appointment,
          id: Date.now().toString()
        };
        
        const appointments = wx.getStorageSync('appointments') || [];
        appointments.push(newAppointment);
        wx.setStorageSync('appointments', appointments);
        
        resolve(newAppointment);
      }, 1000);
    });
    
    // 真实API调用示例：
    // return request({
    //   url: '/appointments',
    //   method: 'POST',
    //   data: appointment
    // });
  }

  static async updateAppointment(appointmentId: string, updates: Partial<Appointment>): Promise<Appointment> {
    // 目前更新本地存储
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const appointments = wx.getStorageSync('appointments') || [];
        const index = appointments.findIndex((a: Appointment) => a.id === appointmentId);
        
        if (index >= 0) {
          appointments[index] = { ...appointments[index], ...updates };
          wx.setStorageSync('appointments', appointments);
          resolve(appointments[index]);
        } else {
          reject(new Error('预约不存在'));
        }
      }, 800);
    });
    
    // 真实API调用示例：
    // return request({
    //   url: `/appointments/${appointmentId}`,
    //   method: 'PUT',
    //   data: updates
    // });
  }

  static async cancelAppointment(appointmentId: string): Promise<boolean> {
    // 目前从本地存储删除
    return new Promise((resolve) => {
      setTimeout(() => {
        const appointments = wx.getStorageSync('appointments') || [];
        const filteredAppointments = appointments.filter((a: Appointment) => a.id !== appointmentId);
        wx.setStorageSync('appointments', filteredAppointments);
        resolve(true);
      }, 800);
    });
    
    // 真实API调用示例：
    // return request({
    //   url: `/appointments/${appointmentId}`,
    //   method: 'DELETE'
    // });
  }

  // AI问诊相关API
  static async sendMessage(message: string, context?: any): Promise<any> {
    // 目前使用本地AI逻辑
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          response: '这是AI的回复',
          suggestions: ['建议1', '建议2', '建议3']
        });
      }, 1500);
    });
    
    // 真实API调用示例：
    // return request({
    //   url: '/ai/consultation',
    //   method: 'POST',
    //   data: { message, context }
    // });
  }

  // 健康资讯API
  static async getHealthArticles(): Promise<any[]> {
    // 模拟健康资讯数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: '1',
            title: '冬季感冒预防小贴士',
            summary: '了解如何在寒冷季节保护自己...',
            image: 'https://img.icons8.com/color/96/health-checkup.png',
            publishTime: '2024-12-14'
          },
          {
            id: '2',
            title: '健康饮食指南',
            summary: '营养均衡的重要性...',
            image: 'https://img.icons8.com/color/96/healthy-food.png',
            publishTime: '2024-12-13'
          }
        ]);
      }, 600);
    });
  }
}

export default ApiService;
