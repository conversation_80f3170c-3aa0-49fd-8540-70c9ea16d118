// 工具函数集合 - JavaScript版本

// 时间格式化
const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return [year, month, day].map(formatNumber).join('/') +
    ' ' + [hour, minute, second].map(formatNumber).join(':')
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : '0' + n
}

// 格式化日期为中文
const formatDateChinese = dateStr => {
  const date = new Date(dateStr);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
  const weekday = weekdays[date.getDay()];
  
  return `${month}月${day}日 周${weekday}`;
}

// 格式化时间段
const formatTimePeriod = period => {
  const periodMap = {
    morning: '上午',
    afternoon: '下午',
    evening: '晚上'
  };
  return periodMap[period] || period;
}

// 计算时间差
const getTimeDiff = targetTime => {
  const now = new Date().getTime();
  const target = new Date(targetTime).getTime();
  const diff = target - now;
  
  if (diff <= 0) return '已过期';
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  
  if (days > 0) return `${days}天后`;
  if (hours > 0) return `${hours}小时后`;
  if (minutes > 0) return `${minutes}分钟后`;
  return '即将开始';
}

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 验证手机号
const validatePhone = phone => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

// 验证身份证号
const validateIdCard = idCard => {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return idCardRegex.test(idCard);
}

// 存储工具
const storage = {
  set: (key, value) => {
    try {
      wx.setStorageSync(key, value);
      return true;
    } catch (error) {
      console.error('存储失败:', error);
      return false;
    }
  },
  
  get: (key, defaultValue = null) => {
    try {
      return wx.getStorageSync(key) || defaultValue;
    } catch (error) {
      console.error('读取存储失败:', error);
      return defaultValue;
    }
  },
  
  remove: key => {
    try {
      wx.removeStorageSync(key);
      return true;
    } catch (error) {
      console.error('删除存储失败:', error);
      return false;
    }
  },
  
  clear: () => {
    try {
      wx.clearStorageSync();
      return true;
    } catch (error) {
      console.error('清空存储失败:', error);
      return false;
    }
  }
};

// Toast提示工具
const toast = {
  success: (title, duration = 2000) => {
    wx.showToast({
      title,
      icon: 'success',
      duration
    });
  },
  
  error: (title, duration = 2000) => {
    wx.showToast({
      title,
      icon: 'error',
      duration
    });
  },
  
  loading: (title = '加载中...') => {
    wx.showLoading({
      title,
      mask: true
    });
  },
  
  hide: () => {
    wx.hideToast();
    wx.hideLoading();
  }
};

module.exports = {
  formatTime,
  formatDateChinese,
  formatTimePeriod,
  getTimeDiff,
  generateId,
  validatePhone,
  validateIdCard,
  storage,
  toast
}
