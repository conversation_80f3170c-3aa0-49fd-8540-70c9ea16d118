{"pages": ["pages/index/index", "pages/ai-consultation/index", "pages/appointment/index", "pages/my-appointments/index", "pages/doctor-list/index", "pages/department-list/index"], "window": {"navigationBarTextStyle": "white", "navigationBarTitleText": "智慧医院", "navigationBarBackgroundColor": "#0052D9", "backgroundColor": "#f5f5f5"}, "tabBar": {"color": "#999999", "selectedColor": "#0052D9", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "assets/icons/home.png", "selectedIconPath": "assets/icons/home-active.png"}, {"pagePath": "pages/my-appointments/index", "text": "我的预约", "iconPath": "assets/icons/appointment.png", "selectedIconPath": "assets/icons/appointment-active.png"}]}, "usingComponents": {"t-button": "miniprogram_npm/tdesign-miniprogram/button/button", "t-card": "miniprogram_npm/tdesign-miniprogram/card/card", "t-grid": "miniprogram_npm/tdesign-miniprogram/grid/grid", "t-grid-item": "miniprogram_npm/tdesign-miniprogram/grid-item/grid-item", "t-navbar": "miniprogram_npm/tdesign-miniprogram/navbar/navbar", "t-message": "miniprogram_npm/tdesign-miniprogram/message/message", "t-dialog": "miniprogram_npm/tdesign-miniprogram/dialog/dialog", "t-input": "miniprogram_npm/tdesign-miniprogram/input/input", "t-cell": "miniprogram_npm/tdesign-miniprogram/cell/cell", "t-cell-group": "miniprogram_npm/tdesign-miniprogram/cell-group/cell-group", "t-avatar": "miniprogram_npm/tdesign-miniprogram/avatar/avatar", "t-tag": "miniprogram_npm/tdesign-miniprogram/tag/tag", "t-divider": "miniprogram_npm/tdesign-miniprogram/divider/divider", "t-calendar": "miniprogram_npm/tdesign-miniprogram/calendar/calendar", "t-picker": "miniprogram_npm/tdesign-miniprogram/picker/picker", "t-timeline": "miniprogram_npm/tdesign-miniprogram/timeline/timeline", "t-timeline-item": "miniprogram_npm/tdesign-miniprogram/timeline-item/timeline-item", "t-indexes": "miniprogram_npm/tdesign-miniprogram/indexes/indexes", "t-toast": "miniprogram_npm/tdesign-miniprogram/toast/toast"}, "style": "v2", "componentFramework": "glass-easel", "lazyCodeLoading": "requiredComponents", "permission": {"scope.userLocation": {"desc": "获取您的位置信息，为您推荐附近的医院"}}}