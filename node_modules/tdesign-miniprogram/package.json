{"name": "tdesign-miniprogram", "purename": "tdesign", "version": "1.10.0", "description": "tdesign-miniprogram", "title": "tdesign-miniprogram", "main": "miniprogram_dist/index.js", "miniprogram": "miniprogram_dist", "keywords": ["tdesign", "miniprogram"], "repository": {"type": "git", "url": "https://github.com/Tencent/tdesign-miniprogram"}, "homepage": "https://tdesign.tencent.com/miniprogram", "scripts": {"start": "npm run dev", "dev": "cross-env NODE_ENV=development gulp dev --gulpfile script/gulpfile.js --cwd ./", "build": "cross-env NODE_ENV=production gulp build --gulpfile script/gulpfile.js --cwd ./", "build:dist": "gulp --gulpfile script/gulpfile.dist.js --cwd ./", "build:example": "gulp --gulpfile script/gulpfile.example.js --cwd ./", "build:assets": "cross-env NODE_ENV=production gulp assets:build --gulpfile script/gulpfile.dist.js --cwd ./", "update:icons": "node script/update-icons.js", "update:css": "node script/generate-css-vars.js", "lintfix": "eslint '{src,example}/**/*.{js,ts}' --fix", "lint": "eslint '{src,example}/**/*.{js,ts}'", "format": "prettier {src,example,script}/**/*.{js,ts,wxss,less,wxml,html,json,md,wxs} --write", "site": "cd site && vite build --configLoader runner", "site:dev": "cd site && vite --configLoader runner", "site:intranet": "cd site && vite build --mode intranet --configLoader runner", "site:prerender": "node script/prerender.mjs", "cover": "jest --coverage", "test": "jest && jest -c jest.e2e.config.js", "test:snap-update": "npm run test:unit -- -u", "test:demo": "node script/gen-demo-test.js", "test:unit": "jest", "test:e2e": "jest -c jest.e2e.config.js", "badge": "node script/coverage-badge.js", "prepare": "husky install", "generate": "gulp generate --gulpfile script/gulpfile.js --cwd ./", "changelog": "node script/generate-changelog.js", "robot": "publish-cli robot-msg", "qrcode": "node script/qrcode/index.js"}, "author": "tdesign", "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-decorators": "^7.18.9", "@babel/plugin-transform-class-properties": "^7.22.3", "@babel/preset-env": "^7.12.11", "@babel/preset-typescript": "^7.12.7", "@commitlint/cli": "^16.0.2", "@commitlint/config-conventional": "^16.0.0", "@rollup/plugin-node-resolve": "^13.0.5", "@types/jest": "^27.0.3", "@types/node": "^20.14.11", "@typescript-eslint/eslint-plugin": "^5.6.0", "@typescript-eslint/parser": "~5.35.0", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/compiler-sfc": "^3.2.4", "axios": "^1.1.3", "babel-jest": "^26.6.3", "commitizen": "^4.2.4", "cross-env": "^7.0.2", "cz-conventional-changelog": "^3.3.0", "dayjs": "^1.10.7", "del": "^6.1.1", "eslint": "^7.0.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-prettier": "^4.0.0", "glob": "^8.1.0", "gray-matter": "^4.0.3", "gulp": "^4.0.2", "gulp-changed": "^4.0.2", "gulp-clean-css": "^4.3.0", "gulp-htmlmin": "^5.0.1", "gulp-if": "^3.0.0", "gulp-insert": "^0.5.0", "gulp-jsonminify": "^1.1.0", "gulp-less": "^5.0.0", "gulp-mp-npm": "^1.9.7", "gulp-plumber": "^1.2.1", "gulp-rename": "^2.0.0", "gulp-replace": "^1.0.0", "gulp-replace-task": "^2.0.1", "gulp-sourcemaps": "^3.0.0", "gulp-terser": "^2.1.0", "gulp-typescript": "^6.0.0-alpha.1", "husky": "^7.0.4", "jest": "^26.6.3", "jest-html-reporter": "^3.3.0", "jsdom": "^20.0.0", "less": "^4.1.1", "lint-staged": "^10.0.0-1", "lodash": "^4.17.21", "merge2": "^1.4.1", "miniprogram-api-typings": "^3.12.3", "miniprogram-automator": "^0.10.0", "miniprogram-simulate": "^1.6.0", "npm-run-all": "^4.1.5", "playwright": "^1.19.1", "prettier": "^2.0.5", "prismjs": "^1.24.1", "standard-changelog": "^2.0.27", "stylelint": "^13.13.1", "tdesign-icons-view": "^0.3.6", "tdesign-publish-cli": "^0.0.12", "tdesign-site-components": "^0.16.0", "tdesign-theme-generator": "^1.1.0", "tinycolor2": "^1.4.2", "tslib": "^2.8.1", "typescript": "~4.7.2", "vite": "^6.2.3", "vite-plugin-tdoc": "^2.0.1", "vue": "^3.2.4", "vue-router": "^4.0.11"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"{src,example,script}/**/*.{js,ts,wxs,wxml,wxss,html,json,less}": ["prettier --write"], "{src,example}/**/*.{js,ts}": ["eslint --fix"]}}