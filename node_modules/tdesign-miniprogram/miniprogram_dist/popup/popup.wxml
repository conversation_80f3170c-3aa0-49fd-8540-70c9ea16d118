<wxs src="./popup.wxs" module="popup"/><wxs src="../common/utils.wxs" module="_"/><view wx:if="{{realVisible}}" style="{{_._style([popup.getPopupStyles(zIndex, distanceTop, placement), style, customStyle])}}" class="{{_.cls(classPrefix, [placement])}} {{transitionClass}} class {{prefix}}-class" bind:transitionend="onTransitionEnd"><view data-prevention="{{preventScrollThrough || (overlayProps ? !!overlayProps.preventScrollThrough : false)}}" bind:touchmove="{{popup.onContentTouchMove}}" class="{{classPrefix}}__content {{prefix}}-class-content"><slot name="content"/><slot/><view class="{{classPrefix}}__close" bind:tap="handleClose"><t-icon name="close" wx:if="{{closeBtn}}" size="64rpx"/><slot name="close-btn" class="{{classPrefix}}-slot"/></view></view></view><t-overlay id="popup-overlay" wx:if="{{showOverlay}}" visible="{{visible}}" usingCustomNavbar="{{usingCustomNavbar}}" z-index="{{overlayProps && overlayProps.zIndex || 11000}}" duration="{{overlayProps && overlayProps.duration || 300}}" background-color="{{overlayProps && overlayProps.backgroundColor || ''}}" prevent-scroll-through="{{preventScrollThrough || (overlayProps ? !!overlayProps.preventScrollThrough : false)}}" bind:tap="handleOverlayClick" custom-style="{{overlayProps && overlayProps.style || ''}}"/>