import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-col`;let Col=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`],this.properties=props,this.data={prefix:prefix,classPrefix:name},this.relations={"../row/row":{type:"parent"}}}};Col=__decorate([wxComponent()],Col);export default Col;