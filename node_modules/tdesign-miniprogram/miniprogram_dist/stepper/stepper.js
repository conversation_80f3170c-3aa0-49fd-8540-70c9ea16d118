import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-stepper`;let Stepper=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-input`,`${prefix}-class-minus`,`${prefix}-class-plus`],this.properties=Object.assign({},props),this.controlledProps=[{key:"value",event:"change"}],this.observers={value(e){this.preValue=Number(e),this.setData({currentValue:this.format(Number(e))})}},this.data={currentValue:0,classPrefix:name,prefix:prefix},this.lifetimes={attached(){const{value:e,min:t}=this.properties;this.setData({currentValue:e?Number(e):t})}},this.methods={isDisabled(e){const{min:t,max:s,disabled:r}=this.properties,{currentValue:i}=this.data;return!!r||("minus"===e&&i<=t||"plus"===e&&i>=s)},getLen(e){const t=e.toString();return-1===t.indexOf(".")?0:t.split(".")[1].length},add(e,t){const s=Math.max(this.getLen(e),this.getLen(t)),r=Math.pow(10,s);return Math.round(e*r+t*r)/r},format(e){const{min:t,max:s,step:r}=this.properties,i=Math.max(this.getLen(r),this.getLen(e));return Math.max(Math.min(s,e,Number.MAX_SAFE_INTEGER),t,Number.MIN_SAFE_INTEGER).toFixed(i)},setValue(e){e=this.format(e),this.preValue!==e&&(this.preValue=e,this._trigger("change",{value:Number(e)}))},minusValue(){if(this.isDisabled("minus"))return this.triggerEvent("overlimit",{type:"minus"}),!1;const{currentValue:e,step:t}=this.data;this.setValue(this.add(e,-t))},plusValue(){if(this.isDisabled("plus"))return this.triggerEvent("overlimit",{type:"plus"}),!1;const{currentValue:e,step:t}=this.data;this.setValue(this.add(e,t))},filterIllegalChar(e){const t=String(e).replace(/[^0-9.]/g,""),s=t.indexOf(".");return this.properties.integer&&-1!==s?t.split(".")[0]:this.properties.integer||-1===s||s===t.lastIndexOf(".")?t:t.split(".",2).join(".")},handleFocus(e){const{value:t}=e.detail;this.triggerEvent("focus",{value:t})},handleInput(e){const{value:t}=e.detail;if(""===t)return;const s=this.filterIllegalChar(t);this.setData({currentValue:s}),this.triggerEvent("input",{value:s})},handleBlur(e){const{value:t}=e.detail,s=this.format(t);this.setValue(s),this.triggerEvent("blur",{value:s})}}}};Stepper=__decorate([wxComponent()],Stepper);export default Stepper;