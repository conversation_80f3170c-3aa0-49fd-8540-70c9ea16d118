import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import props from"./props";import config from"../common/config";const{prefix:prefix}=config,name=`${prefix}-tab-panel`;let TabPanel=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`],this.relations={"../tabs/tabs":{type:"ancestor"}},this.options={multipleSlots:!0},this.properties=props,this.data={prefix:prefix,classPrefix:name,active:!1,hide:!0,id:"",hasActivated:!1},this.observers={"label, badgeProps, disabled, icon, panel, value, lazy"(){this.update()}}}setId(e){this.setData({id:e})}getComputedName(){return null!=this.properties.value?`${this.properties.value}`:`${this.index}`}update(){var e;null===(e=this.$parent)||void 0===e||e.updateTabs()}render(e,t){this.initialized=this.initialized||e,e&&!this.data.hasActivated&&this.setData({hasActivated:!0}),this.setData({active:e,hide:!t.data.animation&&!e})}};TabPanel=__decorate([wxComponent()],TabPanel);export default TabPanel;