import{__decorate}from"tslib";import props from"./props";import config from"../../../common/config";import{SuperComponent,wxComponent}from"../../../common/src/index";const{prefix:prefix}=config,name=`${prefix}-qrcode`;let QRCode=class extends SuperComponent{constructor(){super(...arguments),this.options={multipleSlots:!0},this.properties=Object.assign(Object.assign({},props),{statusRender:{type:Boolean,value:!1}}),this.data={prefix:prefix,classPrefix:name,isSkyline:!1},this.lifetimes={attached(){this.setData({isSkyline:"skyline"===this.renderer})}},this.methods={handleRefresh(){this.triggerEvent("refresh")}}}};QRCode=__decorate([wxComponent()],QRCode);export default QRCode;