import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config;let Row=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[],this.properties=props,this.data={prefix:prefix},this.relations={"../col/col":{type:"child",linked(t){const{gutter:o}=this.data;o&&t.setData({gutter:o})}}},this.observers={gutter(){this.setGutter()}},this.methods={setGutter(){const{gutter:t}=this.data;this.$children.forEach(o=>{o.setData({gutter:t})})}}}};Row=__decorate([wxComponent()],Row);export default Row;