import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{getRect}from"../common/utils";import{getObserver}from"../common/wechat";let ARRAY=[];const{prefix:prefix}=config,name=`${prefix}-swipe-cell`,ContainerClass=`.${name}`;let SwiperCell=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`],this.options={multipleSlots:!0},this.properties=props,this.data={prefix:prefix,wrapperStyle:"",closed:!0,classPrefix:name,skipMove:!1},this.observers={"left, right"(){this.setSwipeWidth()}},this.lifetimes={attached(){ARRAY.push(this)},ready(){this.setSwipeWidth()},detached(){ARRAY=ARRAY.filter(e=>e!==this)}}}setSwipeWidth(){Promise.all([getRect(this,`${ContainerClass}__left`),getRect(this,`${ContainerClass}__right`)]).then(([e,t])=>{0!==e.width||0!==t.width||this._hasObserved||(this._hasObserved=!0,getObserver(this,`.${name}`).then(()=>{this.setSwipeWidth()})),this.setData({leftWidth:e.width,rightWidth:t.width})})}skipMove(){this.data.skipMove||this.setData({skipMove:!0})}catchMove(){this.data.skipMove&&this.setData({skipMove:!1})}open(){this.setData({opened:!0})}close(){this.setData({opened:!1})}closeOther(){ARRAY.filter(e=>e!==this).forEach(e=>e.close())}onTap(){this.close()}onActionTap(e){const{currentTarget:{dataset:{action:t}}}=e;this.triggerEvent("click",t)}};SwiperCell=__decorate([wxComponent()],SwiperCell);export default SwiperCell;