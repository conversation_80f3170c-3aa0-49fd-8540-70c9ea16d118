import{__decorate}from"tslib";import{wxComponent,SuperComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{classNames,calcIcon}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-tag`;let CheckTag=class extends SuperComponent{constructor(){super(...arguments),this.data={prefix:prefix,classPrefix:name,className:""},this.properties=props,this.externalClasses=[`${prefix}-class`],this.controlledProps=[{key:"checked",event:"change"}],this.options={multipleSlots:!0},this.lifetimes={attached(){this.setClass()}},this.observers={"size, disabled, checked"(){this.setClass()},icon(e){this.setData({_icon:calcIcon(e)})}},this.methods={setClass(){const{classPrefix:e}=this.data,{size:s,variant:t,disabled:i,checked:a,shape:c}=this.properties,o=classNames([e,`${e}--checkable`,i?`${e}--disabled`:"",a?`${e}--checked`:"",`${e}--${a?"primary":"default"}`,`${e}--${s}`,`${e}--${t}`,`${e}--${c}`]);this.setData({className:o})},onClick(){if(this.data.disabled)return;const{checked:e}=this.data;this._trigger("click"),this._trigger("change",{checked:!e})},onClose(e){this.data.disabled||this._trigger("close",e)}}}};CheckTag=__decorate([wxComponent()],CheckTag);export default CheckTag;