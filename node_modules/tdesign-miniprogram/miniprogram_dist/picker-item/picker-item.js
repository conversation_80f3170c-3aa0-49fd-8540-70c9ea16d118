import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-picker-item`,ANIMATION_DURATION=1e3,INERTIA_TIME=300,INERTIA_DISTANCE=15,range=function(t,e,i){return Math.min(Math.max(t,e),i)},momentum=(t,e)=>{let i=t;return i=Math.abs(i/e)/.005*(i<0?-1:1),i};let PickerItem=class extends SuperComponent{constructor(){super(...arguments),this.relations={"../picker/picker":{type:"parent",linked(t){if("keys"in t.data){const{keys:e}=t.data;if(null===e||JSON.stringify(this.data.pickerKeys)===JSON.stringify(e))return;this.setData({pickerKeys:e})}}}},this.options={multipleSlots:!0},this.externalClasses=[`${prefix}-class`],this.properties=props,this.observers={"options, pickerKeys"(){this.update()}},this.data={prefix:prefix,classPrefix:name,offset:0,duration:0,value:"",curIndex:0,columnIndex:0,pickerKeys:{value:"value",label:"label"},formatOptions:props.options.value},this.lifetimes={created(){this.StartY=0,this.StartOffset=0,this.startTime=0}},this.methods={onClickItem(t){const{index:e}=t.currentTarget.dataset,{pickItemHeight:i}=this.data,s=range(e,0,this.getCount()-1);s!==this._selectedIndex&&this.setData({offset:-s*i,curIndex:s,duration:200}),this.updateSelected(s,!0)},onTouchStart(t){this.StartY=t.touches[0].clientY,this.StartOffset=this.data.offset,this.startTime=Date.now(),this.setData({duration:0})},onTouchMove(t){const{StartY:e,StartOffset:i}=this,{pickItemHeight:s}=this.data,o=t.touches[0].clientY-e,n=range(i+o,-this.getCount()*s,0);this.setData({offset:n})},onTouchEnd(t){const{offset:e,pickItemHeight:i}=this.data,{startTime:s}=this;if(e===this.StartOffset)return;let o=0;const n=t.changedTouches[0].clientY-this.StartY,a=Date.now()-s;a<300&&Math.abs(n)>15&&(o=momentum(n,a));const r=range(e+o,-this.getCount()*i,0),c=range(Math.round(-r/i),0,this.getCount()-1);this.setData({offset:-c*i,duration:1e3,curIndex:c}),c!==this._selectedIndex&&this.updateSelected(c,!0)},formatOption:(t,e,i)=>"function"!=typeof i?t:t.map(t=>i(t,e)),updateSelected(t,e){var i,s,o;const{columnIndex:n,pickerKeys:a,formatOptions:r}=this.data;this._selectedIndex=t,this._selectedValue=null===(i=r[t])||void 0===i?void 0:i[null==a?void 0:a.value],this._selectedLabel=null===(s=r[t])||void 0===s?void 0:s[null==a?void 0:a.label],e&&(null===(o=this.$parent)||void 0===o||o.triggerColumnChange({index:t,column:n}))},update(){const{options:t,value:e,pickerKeys:i,pickItemHeight:s,format:o,columnIndex:n}=this.data,a=this.formatOption(t,n,o),r=a.findIndex(t=>t[null==i?void 0:i.value]===e),c=r>0?r:0;this.setData({formatOptions:a,offset:-c*s,curIndex:c},()=>{this.updateSelected(c,!1)})},getCount(){var t,e;return null===(e=null===(t=this.data)||void 0===t?void 0:t.options)||void 0===e?void 0:e.length}}}};PickerItem=__decorate([wxComponent()],PickerItem);export default PickerItem;