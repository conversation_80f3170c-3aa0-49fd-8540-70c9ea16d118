import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{getRect,systemInfo,unitConvert}from"../common/utils";import{canUseProxyScrollView}from"../common/version";const{prefix:prefix}=config,name=`${prefix}-pull-down-refresh`,defaultLoadingTexts=["下拉刷新","松手刷新","正在刷新","刷新完成"];let PullDownRefresh=class extends SuperComponent{constructor(){super(...arguments),this.pixelRatio=1,this.startPoint=null,this.isPulling=!1,this.maxRefreshAnimateTimeFlag=0,this.closingAnimateTimeFlag=0,this.refreshStatusTimer=null,this.externalClasses=[`${prefix}-class`,`${prefix}-class-loading`,`${prefix}-class-text`,`${prefix}-class-indicator`],this.behaviors=canUseProxyScrollView()?["wx://proxy-scroll-view"]:[],this.options={multipleSlots:!0,pureDataPattern:/^_/},this.relations={"../back-top/back-top":{type:"descendant"}},this.properties=props,this.data={prefix:prefix,classPrefix:name,distanceTop:0,barHeight:0,tipsHeight:0,refreshStatus:-1,loosing:!1,enableToRefresh:!0,scrollTop:0,_maxBarHeight:0,_loadingBarHeight:0},this.lifetimes={attached(){const{screenWidth:t}=systemInfo,{loadingTexts:e,maxBarHeight:s,loadingBarHeight:i}=this.properties,r=Array.isArray(e)&&e.length>=4;this.setData({_maxBarHeight:unitConvert(s),_loadingBarHeight:unitConvert(i),loadingTexts:r?e:defaultLoadingTexts}),this.pixelRatio=750/t,getRect(this,`.${name}`).then(t=>{this.setData({distanceTop:t.top})})},detached(){clearTimeout(this.maxRefreshAnimateTimeFlag),clearTimeout(this.closingAnimateTimeFlag),this.resetTimer()}},this.observers={value(t){t?this.doRefresh():(clearTimeout(this.maxRefreshAnimateTimeFlag),this.data.refreshStatus>0&&this.setData({refreshStatus:3}),this.setData({barHeight:0}))},barHeight(t){this.resetTimer(),0===t&&-1!==this.data.refreshStatus&&(this.refreshStatusTimer=setTimeout(()=>{this.setData({refreshStatus:-1})},240)),this.setData({tipsHeight:Math.min(t,this.data._loadingBarHeight)})},maxBarHeight(t){this.setData({_maxBarHeight:unitConvert(t)})},loadingBarHeight(t){this.setData({_loadingBarHeight:unitConvert(t)})}},this.methods={resetTimer(){this.refreshStatusTimer&&(clearTimeout(this.refreshStatusTimer),this.refreshStatusTimer=null)},onScrollToBottom(){this.triggerEvent("scrolltolower")},onScrollToTop(){this.setData({enableToRefresh:!0})},onScroll(t){const{scrollTop:e}=t.detail;this.setData({enableToRefresh:0===e}),this.triggerEvent("scroll",{scrollTop:e})},onTouchStart(t){if(this.isPulling||!this.data.enableToRefresh||this.properties.disabled)return;const{touches:e}=t;if(1!==e.length)return;const{pageX:s,pageY:i}=e[0];this.setData({loosing:!1}),this.startPoint={pageX:s,pageY:i},this.isPulling=!0},onTouchMove(t){if(!this.startPoint||this.properties.disabled)return;const{touches:e}=t;if(1!==e.length)return;const{pageY:s}=e[0],i=s-this.startPoint.pageY;i>0&&this.setRefreshBarHeight(i)},onTouchEnd(t){if(!this.startPoint||this.properties.disabled)return;const{changedTouches:e}=t;if(1!==e.length)return;const{pageY:s}=e[0],i=s-this.startPoint.pageY;this.startPoint=null,this.isPulling=!1,this.setData({loosing:!0}),i>this.data._loadingBarHeight?(this._trigger("change",{value:!0}),this.triggerEvent("refresh")):this.setData({barHeight:0})},onDragStart(t){const{scrollTop:e,scrollLeft:s}=t.detail;this.triggerEvent("dragstart",{scrollTop:e,scrollLeft:s})},onDragging(t){const{scrollTop:e,scrollLeft:s}=t.detail;this.triggerEvent("dragging",{scrollTop:e,scrollLeft:s})},onDragEnd(t){const{scrollTop:e,scrollLeft:s}=t.detail;this.triggerEvent("dragend",{scrollTop:e,scrollLeft:s})},doRefresh(){this.properties.disabled||(this.setData({barHeight:this.data._loadingBarHeight,refreshStatus:2,loosing:!0}),this.maxRefreshAnimateTimeFlag=setTimeout(()=>{this.maxRefreshAnimateTimeFlag=null,2===this.data.refreshStatus&&(this.triggerEvent("timeout"),this._trigger("change",{value:!1}))},this.properties.refreshTimeout))},setRefreshBarHeight(t){const e=Math.min(t,this.data._maxBarHeight),s={barHeight:e};return e>=this.data._loadingBarHeight?s.refreshStatus=1:s.refreshStatus=0,new Promise(t=>{this.setData(s,()=>t(e))})},setScrollTop(t){this.setData({scrollTop:t})},scrollToTop(){this.setScrollTop(0)}}}};PullDownRefresh=__decorate([wxComponent()],PullDownRefresh);export default PullDownRefresh;