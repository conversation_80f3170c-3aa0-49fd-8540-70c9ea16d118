import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import{isDef}from"../common/validator";import config from"../common/config";import{getTreeDepth}from"../common/utils";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-tree-select`;let TreeSelect=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-left-column`,`${prefix}-class-left-item`,`${prefix}-class-middle-item`,`${prefix}-class-right-column`,`${prefix}-class-right-item`,`${prefix}-class-right-item-label`],this.options={multipleSlots:!0},this.data={prefix:prefix,classPrefix:name,scrollIntoView:null},this.properties=props,this.controlledProps=[{key:"value",event:"change"}],this.observers={"value, customValue, options, keys, multiple"(){this.buildTreeOptions()}},this.lifetimes={ready(){this.getScrollIntoView("init")}},this.methods={buildTreeOptions(){var e,l;const{options:t,value:i,customValue:o,multiple:n,keys:s}=this.data;if(!t.length)return;const r=[];let a=-1,c={children:t};for(;null==c?void 0:c.children;){a+=1;const t=c.children.map(e=>({label:e[(null==s?void 0:s.label)||"label"],value:e[(null==s?void 0:s.value)||"value"],disabled:e[(null==s?void 0:s.disabled)||"disabled"],children:e[(null==s?void 0:s.children)||"children"]}));r.push(t);const n=null!==(e=null==o?void 0:o[a])&&void 0!==e?e:null==i?void 0:i[a];c=n&&null!==(l=t.find(e=>e.value===n))&&void 0!==l?l:t[0]}const u=getTreeDepth(t,null==s?void 0:s.children);for(;r.length<u;)r.push([]),a+=1;const d=Math.max(0,a),p=o||r.map((e,l)=>{var t,o,s;const a=l===r.length-1&&n?[null===(t=e[0])||void 0===t?void 0:t.value]:null===(o=e[0])||void 0===o?void 0:o.value;return null!==(s=null==i?void 0:i[l])&&void 0!==s?s:a});this.setData({innerValue:p,leafLevel:d,treeOptions:r})},getScrollIntoView(e){const{value:l,customValue:t,scrollIntoView:i}=this.data;if("init"===e){const e=t||l,i=Array.isArray(e)?e.map(e=>Array.isArray(e)?e[0]:e):[e];this.setData({scrollIntoView:i})}else{if(null===i)return;this.setData({scrollIntoView:null})}},onRootChange(e){const{innerValue:l}=this.data,{value:t}=e.detail;this.getScrollIntoView("none"),l[0]=t,this._trigger("change",{value:l,level:0})},handleTreeClick(e){const{level:l,value:t}=e.currentTarget.dataset,{innerValue:i}=this.data;i[l]=t,this.getScrollIntoView("none"),this._trigger("change",{value:i,level:1})},handleChange(e){const{innerValue:l}=this.data,{level:t,type:i}=e.target.dataset,{value:o}="multiple"===i?e.detail.context:e.detail;if("multiple"===i){isDef(l[t])||(l[t]=[]);const e=l[t].indexOf(o);-1===e?l[t].push(o):l[t].splice(e,1)}else l[t]=o;this.getScrollIntoView("none"),this._trigger("change",{value:l,level:t})}}}};TreeSelect=__decorate([wxComponent()],TreeSelect);export default TreeSelect;