import{__awaiter,__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import{trimSingleValue,trimValue}from"./tool";import props from"./props";import{getRect}from"../common/utils";import Bus from"../common/bus";const{prefix:prefix}=config,name=`${prefix}-slider`;let Slider=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-bar`,`${prefix}-class-bar-active`,`${prefix}-class-bar-disabled`,`${prefix}-class-cursor`],this.options={pureDataPattern:/^__/},this.properties=props,this.controlledProps=[{key:"value",event:"change"}],this.data={sliderStyles:"",classPrefix:name,initialLeft:null,initialRight:null,activeLeft:0,activeRight:0,maxRange:0,lineLeft:0,lineRight:0,dotTopValue:[0,0],_value:0,blockSize:20,isScale:!1,scaleArray:[],scaleTextArray:[],prefix:prefix,isVisibleToScreenReader:!1,identifier:[-1,-1],__inited:!1},this.observers={value(e){this.handlePropsChange(e)},_value(e){this.bus.on("initial",()=>this.renderLine(e)),this.toggleA11yTips()},marks(e){this.bus.on("initial",()=>this.handleMark(e))}},this.lifetimes={created(){this.bus=new Bus},attached(){const{value:e}=this.properties;e||this.handlePropsChange(0),this.init(),this.injectPageScroll()}}}injectPageScroll(){const{range:e,vertical:t}=this.properties;if(!e||!t)return;const i=getCurrentPages()||[];let s=null;if(i&&i.length-1>=0&&(s=i[i.length-1]),!s)return;const r=null==s?void 0:s.onPageScroll;s.onPageScroll=e=>{null==r||r.call(this,e),this.observerScrollTop(e)}}observerScrollTop(e){const{scrollTop:t}=e||{};this.pageScrollTop=t}toggleA11yTips(){this.setData({isVisibleToScreenReader:!0}),setTimeout(()=>{this.setData({isVisibleToScreenReader:!1})},2e3)}renderLine(e){const{min:t,max:i,range:s}=this.properties,{maxRange:r}=this.data;if(s){const s=r*(e[0]-Number(t))/(Number(i)-Number(t)),a=r*(Number(i)-e[1])/(Number(i)-Number(t));this.setLineStyle(s,a)}else this.setSingleBarWidth(e)}triggerValue(e){this.preval!==e&&(this.preval=e,this._trigger("change",{value:trimValue(e,this.properties)}))}handlePropsChange(e){const t=trimValue(e,this.properties),i=()=>{this.setData({_value:t})};0!==this.data.maxRange?i():this.init().then(i)}valueToPosition(e){const{min:t,max:i,theme:s}=this.properties,{blockSize:r,maxRange:a}=this.data,n="capsule"===s?Number(r)/2:0;return Math.round((Number(e)-Number(t))/(Number(i)-Number(t))*a)+n}handleMark(e){const t=e=>e.map(e=>({val:e,left:this.valueToPosition(e)}));if((null==e?void 0:e.length)&&Array.isArray(e)&&this.setData({isScale:!0,scaleArray:t(e),scaleTextArray:[]}),"[object Object]"===Object.prototype.toString.call(e)){const i=Object.keys(e).map(e=>Number(e)),s=i.map(t=>e[t]);this.setData({isScale:i.length>0,scaleArray:t(i),scaleTextArray:s})}}setSingleBarWidth(e){const t=this.valueToPosition(e);this.setData({lineBarWidth:`${t}px`})}init(){return __awaiter(this,void 0,void 0,function*(){if(this.data.__inited)return;const e=yield getRect(this,"#sliderLine"),{blockSize:t}=this.data,{theme:i,vertical:s}=this.properties,r=Number(t)/2,{top:a,bottom:n,right:o,left:l}=e;let h=s?n-a:o-l,c=s?a:l,u=s?n:o;0===c&&0===u||("capsule"===i&&(h=h-Number(t)-6,c-=r,u-=r),this.setData({maxRange:h,initialLeft:c,initialRight:u,__inited:!0}),this.bus.emit("initial"))})}stepValue(e){const{step:t,min:i,max:s}=this.properties,r=String(t).indexOf(".")>-1?String(t).length-String(t).indexOf(".")-1:0;return trimSingleValue(Number((Math.round(e/Number(t))*Number(t)).toFixed(r)),Number(i),Number(s))}onSingleLineTap(e){const{disabled:t}=this.properties;if(t)return;const i=-1===this.data.identifier[0];if(i){const[t]=e.changedTouches;this.data.identifier[0]=t.identifier}const s=this.getSingleChangeValue(e);i&&(this.data.identifier[0]=-1),this.triggerValue(s)}getSingleChangeValue(e){const{min:t,max:i,theme:s,vertical:r}=this.properties,{initialLeft:a,maxRange:n,blockSize:o}=this.data,l=e.changedTouches.find(e=>e.identifier===this.data.identifier[0]),h=this.getPagePosition(l);let c=0;"capsule"===s?(c=Number(o),r&&(c*=2),c+=6):r&&(c=Number(o));const u=h-a-c;let p=0;return p=u<=0?Number(t):u>=n?Number(i):u/n*(Number(i)-Number(t))+Number(t),this.stepValue(p)}convertPosToValue(e,t){const{maxRange:i}=this.data,{max:s,min:r}=this.properties;return 0===t?e/i*(Number(s)-Number(r))+Number(r):Number(s)-e/i*(Number(s)-Number(r))}onLineTap(e){const{disabled:t,theme:i,vertical:s}=this.properties,{initialLeft:r,initialRight:a,maxRange:n,blockSize:o}=this.data;if(t)return;const[l]=e.changedTouches,h=this.getPagePosition(l),c="capsule"===i?Number(o)/2:0;h-r<0||-(h-a)>n+Number(o)||Promise.all([getRect(this,"#leftDot"),getRect(this,"#rightDot")]).then(([e,t])=>{const n=this.pageScrollTop||0,l=s?e.top+n:e.left,u=Math.abs(h-l-c),p=s?t.top+n:t.left,d=u<Math.abs(p-h+c);let g=0;if("capsule"===i?(g=Number(o),s&&(g*=2),g+=6):s&&(g=Number(o)),d){const e=h-r-g,t=this.convertPosToValue(e,0);this.triggerValue([this.stepValue(t),this.data._value[1]])}else{let e=-(h-a);s&&(e+=g/2);const t=this.convertPosToValue(e,1);this.triggerValue([this.data._value[0],this.stepValue(t)])}})}onTouchStart(e){this.triggerEvent("dragstart",{e:e});const[t]=e.changedTouches;"rightDot"===e.currentTarget.id?this.data.identifier[1]=t.identifier:this.data.identifier[0]=t.identifier}onTouchMoveLeft(e){const{disabled:t,theme:i,vertical:s}=this.properties,{initialLeft:r,_value:a,blockSize:n}=this.data;if(t)return;const o=e.changedTouches.find(e=>e.identifier===this.data.identifier[0]),l=this.getPagePosition(o);let h=0;"capsule"===i&&(h+=Number(n)),s&&(h+=Number(n)+6);const c=l-r-h,u=[...a],p=this.convertPosToValue(c,0);u[0]=this.stepValue(p),this.triggerValue(u)}onTouchMoveRight(e){const{disabled:t,vertical:i}=this.properties,{initialRight:s,_value:r,blockSize:a}=this.data;if(t)return;const n=e.changedTouches.find(e=>e.identifier===this.data.identifier[1]),o=this.getPagePosition(n);let l=0;i&&(l+=Number(a)/2+6);const h=-(o-s-l),c=[...r],u=this.convertPosToValue(h,1);c[1]=this.stepValue(u),this.triggerValue(c)}setLineStyle(e,t){const{theme:i}=this.properties,{blockSize:s,maxRange:r}=this.data,a="capsule"===i?Number(s)/2:0,[n,o]=this.data._value,l=e=>parseInt(e,10);this.setData({dotTopValue:[n,o]}),e+t<=r?this.setData({lineLeft:l(e+a),lineRight:l(t+a)}):this.setData({lineLeft:l(r+a-t),lineRight:l(r-e+1.5*a)})}onTouchEnd(e){this.triggerEvent("dragend",{e:e,value:this.data._value}),"rightDot"===e.currentTarget.id?this.data.identifier[1]=-1:this.data.identifier[0]=-1}getPagePosition(e){const{pageX:t,pageY:i}=e,{vertical:s}=this.properties;return s?i:t}};Slider=__decorate([wxComponent()],Slider);export default Slider;