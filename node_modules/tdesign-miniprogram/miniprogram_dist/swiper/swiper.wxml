<wxs src="./index.wxs" module="_this"/><wxs src="../common/utils.wxs" module="_"/><import src="../common/template/image.wxml"/><view class="class {{prefix}}-class {{classPrefix}}" style="{{_._style([style, customStyle])}}"><swiper class="{{classPrefix}}-host" autoplay="{{autoplay}}" current="{{current}}" interval="{{interval}}" duration="{{duration}}" circular="{{loop}}" vertical="{{direction == 'vertical'}}" easing-function="{{easingFunction}}" previous-margin="{{previousMargin}}" next-margin="{{nextMargin}}" snap-to-edge="{{snapToEdge}}" display-multiple-items="{{displayMultipleItems}}" style="height: {{_.addUnit(height)}}" bindchange="onChange"><swiper-item wx:for="{{list}}" wx:key="index" class="{{_.cls(classPrefix + '__item', [['preview', _this.isPrev(navCurrent, index, list)], ['next', _this.isNext(navCurrent, index, list)]])}}" data-index="{{index}}" bind:tap="onTap" aria-hidden="{{navCurrent !== index}}" aria-role="image" aria-label="{{_.isObject(item) ? item.ariaLabel : ''}}"><template is="image" data="{{tClass: _this.getImageClass(prefix, navCurrent, index, list), style: 'height: ' + _.addUnit(height), src: _.isObject(item) ? item.value : item, mode: 'aspectFill', dataset: index, ...imageProps, bindload: 'onImageLoad' }}"/></swiper-item></swiper><t-swiper-nav wx:if="{{navigation}}" t-class="{{prefix}}-class-nav" type="{{navigation.type || 'dots'}}" current="{{navCurrent || 0}}" total="{{list.length || 0}}" direction="{{direction || 'horizontal'}}" pagination-position="{{paginationPosition || 'bottom'}}" min-show-num="{{navigation.minShowNum || 2}}" show-controls="{{navigation.showControls || false}}" bind:nav-btn-change="onNavBtnChange"/><slot name="navigation"/><slot name="nav"/></view>