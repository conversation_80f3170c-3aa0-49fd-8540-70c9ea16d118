{"key": "TDesign", "label": "Tdesign", "components": {"t-action-sheet": {"key": "t-action-sheet", "label": "动作面板", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-actionsheet.png", "properties": [{"key": "cancelText", "type": ["String"], "desc": "设置取消按钮的文本", "label": ""}, {"key": "count", "type": ["Number"], "desc": "设置每页展示菜单的数量，仅当 type=grid 时有效", "label": ""}, {"key": "items", "type": ["Array"], "desc": "菜单项", "label": ""}, {"key": "showCancel", "type": ["Boolean"], "desc": "是否显示取消按钮", "label": ""}, {"key": "theme", "type": ["String"], "desc": "展示类型，列表和表格形式展示", "label": ""}, {"key": "visible", "type": ["Boolean"], "desc": "显示与隐藏", "label": ""}], "events": [{"key": "bind:cancel", "desc": "点击取消按钮时触发", "label": ""}, {"key": "bind:close", "desc": "关闭时触发", "label": ""}, {"key": "bind:selected", "desc": "选择菜单项时触发", "label": ""}], "tpl": "<t-action-sheet id=\"t-action-sheet-imperative\" visible=\"{{false}}\"></t-action-sheet>", "path": "./action-sheet/action-sheet"}, "t-avatar-group": {"key": "t-avatar-group", "label": "头像组", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-avatar.png", "properties": [{"key": "cascading", "type": ["String"], "desc": "图片之间的层叠关系，可选值：左侧图片在上和右侧图片在上", "label": ""}, {"key": "collapseAvatar", "type": ["String"], "desc": "头像数量超出时，会出现一个头像折叠元素。该元素内容可自定义。默认为 `+N`。示例：`+5`，`...`, `更多`", "label": ""}, {"key": "max", "type": ["Number"], "desc": "能够同时显示的最多头像数量", "label": ""}, {"key": "size", "type": ["String"], "desc": "尺寸，示例值：small/medium/large/24px/38px 等。优先级低于 Avatar.size", "label": ""}], "externalClasses": ["t-class", "t-class-image", "t-class-content"], "tpl": "<t-avatar-group max=\"2\" collapseAvatar=\"+5\"><t-avatar wx:for=\"{{['https://cdn-we-retail.ym.tencent.com/retail-ui/components-exp/avatar/avatar-v2/1.png','https://cdn-we-retail.ym.tencent.com/retail-ui/components-exp/avatar/avatar-v2/2.png','https://cdn-we-retail.ym.tencent.com/retail-ui/components-exp/avatar/avatar-v2/3.png']}}\" wx:key=\"index\" image=\"{{item}}\" style=\"margin-right: -16rpx\" /></t-avatar-group>", "require": {"t-avatar": "./avatar/avatar"}, "path": "./avatar/avatar-group"}, "t-avatar": {"key": "t-avatar", "label": "头像", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-avatar.png", "properties": [{"key": "alt", "type": ["String"], "desc": "头像替换文本，仅当图片加载失败时有效", "label": ""}, {"key": "badgeProps", "type": ["Object"], "desc": "头像右上角提示信息，继承 Badge 组件的全部特性。如：小红点，或者数字", "label": ""}, {"key": "hideOnLoadFailed", "type": ["Boolean"], "desc": "加载失败时隐藏图片", "label": ""}, {"key": "icon", "type": ["String"], "desc": "图标", "label": ""}, {"key": "image", "type": ["String"], "desc": "图片地址", "label": ""}, {"key": "shape", "type": ["String"], "desc": "形状", "label": ""}, {"key": "size", "type": ["String"], "desc": "尺寸，示例值：small/medium/large/24px/38px 等，默认为 large", "label": ""}], "externalClasses": ["t-class"], "events": [{"key": "bind:error", "desc": "图片加载失败时触发", "label": ""}], "tpl": "<t-avatar icon=\"user\" alt=\"avatar\"/>", "path": "./avatar/avatar"}, "t-back-top": {"key": "t-back-top", "label": "回到顶部", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-backtop.png", "properties": [{"key": "fixed", "type": ["Boolean"], "desc": "是否绝对定位固定到屏幕右下方", "label": ""}, {"key": "icon", "type": ["String"], "desc": "图标", "label": ""}, {"key": "text", "type": ["String"], "desc": "文案", "label": ""}, {"key": "theme", "type": ["String"], "desc": "预设的样式类型", "label": ""}], "externalClasses": ["t-class", "t-class-icon", "t-class-text"], "events": [{"key": "bind:to-top", "desc": "点击触发", "label": ""}], "tpl": "<t-back-top />", "path": "./back-top/back-top"}, "t-badge": {"key": "t-badge", "label": "徽标数", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-badge.png", "properties": [{"key": "color", "type": ["String"], "desc": "颜色", "label": ""}, {"key": "content", "type": ["String"], "desc": "徽标内容，示例：`content='自定义内容'`。也可以使用默认插槽定义", "label": ""}, {"key": "count", "type": ["String", "Number"], "desc": "徽标右上角内容。可以是数字，也可以是文字。如：'new'/3/99+。特殊：值为空表示使用插槽渲染", "label": ""}, {"key": "dot", "type": ["Boolean"], "desc": "是否为红点", "label": ""}, {"key": "maxCount", "type": ["Number"], "desc": "封顶的数字值", "label": ""}, {"key": "offset", "type": ["Array"], "desc": "设置状态点的位置偏移，示例：[-10, 20] 或 ['10em', '8rem']", "label": ""}, {"key": "shape", "type": ["String"], "desc": "形状", "label": ""}, {"key": "showZero", "type": ["Boolean"], "desc": "当数值为 0 时，是否展示徽标", "label": ""}, {"key": "size", "type": ["String"], "desc": "尺寸", "label": ""}], "externalClasses": ["t-class", "t-class-content", "t-class-count"], "tpl": "<t-badge content=\"消息\" dot />", "path": "./badge/badge"}, "t-button": {"key": "t-button", "label": "按钮", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-button.png", "properties": [{"key": "block", "type": ["Boolean"], "desc": "是否为块级元素", "label": ""}, {"key": "content", "type": ["String"], "desc": "按钮内容", "label": ""}, {"key": "customDataset", "type": ["Object"], "desc": "自定义 dataset，可通过 event.currentTarget.dataset.custom 获取", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "是否禁用按钮", "label": ""}, {"key": "ghost", "type": ["Boolean"], "desc": "是否为幽灵按钮（镂空按钮）", "label": ""}, {"key": "icon", "type": ["String"], "desc": "图标名称", "label": ""}, {"key": "loading", "type": ["Boolean"], "desc": "是否显示为加载状态", "label": ""}, {"key": "shape", "type": ["String"], "desc": "按钮形状，有 4 种：长方形、正方形、圆角长方形、圆形", "label": ""}, {"key": "size", "type": ["String"], "desc": "组件尺寸", "label": ""}, {"key": "theme", "type": ["String"], "desc": "组件风格，依次为品牌色、危险色", "label": ""}, {"key": "type", "type": ["String"], "desc": "同小程序的 formType", "label": ""}, {"key": "variant", "type": ["String"], "desc": "按钮形式，基础、线框、文字", "label": ""}], "externalClasses": ["t-class", "t-class-icon"], "events": [{"key": "bind:click", "desc": "点击时触发", "label": ""}], "tpl": "<t-button theme=\"primary\" block content=\"强按钮\"></t-button>", "path": "./button/button"}, "t-cell-group": {"key": "t-cell-group", "label": "单元格组", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-cell.png", "properties": [{"key": "bordered", "type": ["Boolean"], "desc": "是否显示组边框", "label": ""}, {"key": "title", "type": ["String"], "desc": "单元格组标题", "label": ""}], "externalClasses": ["t-class"], "tpl": "<t-cell-group title=\"01 基础用法\"><t-cell title=\"单行标题\" required /><t-cell title=\"单行标题\" hover note=\"辅助信息\" /><t-cell title=\"单行标题\" hover arrow /><t-cell title=\"单行标题\" hover note=\"辅助信息\" arrow /></t-cell-group>", "require": {"t-cell": "./cell/cell"}, "path": "./cell-group/cell-group"}, "t-cell": {"key": "t-cell", "label": "单元格", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-cell.png", "properties": [{"key": "align", "type": ["String"], "desc": "内容的对齐方式，默认居中对齐", "label": ""}, {"key": "arrow", "type": ["Boolean"], "desc": "是否显示右侧箭头", "label": ""}, {"key": "bordered", "type": ["Boolean"], "desc": "是否显示下边框", "label": ""}, {"key": "description", "type": ["String"], "desc": "下方内容描述", "label": ""}, {"key": "hover", "type": ["Boolean"], "desc": "是否开启点击反馈", "label": ""}, {"key": "image", "type": ["String"], "desc": "主图", "label": ""}, {"key": "jumpType", "type": ["String"], "desc": "链接跳转类型", "label": ""}, {"key": "leftIcon", "type": ["String"], "desc": "左侧图标，出现在单元格标题的左侧", "label": ""}, {"key": "note", "type": ["String"], "desc": "和标题同行的说明文字", "label": ""}, {"key": "required", "type": ["Boolean"], "desc": "是否显示表单必填星号", "label": ""}, {"key": "rightIcon", "type": ["String"], "desc": "最右侧图标", "label": ""}, {"key": "title", "type": ["String"], "desc": "标题", "label": ""}, {"key": "url", "type": ["String"], "desc": "点击后跳转链接地址。如果值为空，则表示不需要跳转", "label": ""}], "externalClasses": ["t-class", "t-class-title", "t-class-note", "t-class-description", "t-class-thumb", "t-class-hover", "t-class-left", "t-class-right"], "events": [{"key": "bind:click", "desc": "右侧内容", "label": ""}], "tpl": "<t-cell title=\"单行标题\" hover />", "path": "./cell/cell"}, "t-check-tag": {"key": "t-check-tag", "label": "可选标签", "icon": "", "properties": [{"key": "checked", "type": ["Boolean"], "desc": "标签选中的状态，默认风格（theme=default）才有选中态", "label": ""}, {"key": "closable", "type": ["Boolean"], "desc": "标签是否可关闭", "label": ""}, {"key": "content", "type": ["String", "Number"], "desc": "组件子元素", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "标签禁用态，失效标签不能触发事件。默认风格（theme=default）才有禁用态", "label": ""}, {"key": "icon", "type": ["String"], "desc": "标签中的图标，可自定义图标呈现", "label": ""}, {"key": "shape", "type": ["String"], "desc": "标签类型，有三种：方形、圆角方形、标记型", "label": ""}, {"key": "size", "type": ["String"], "desc": "标签尺寸", "label": ""}], "events": [{"key": "bind:change", "desc": "组件子元素", "label": ""}, {"key": "bind:click", "desc": "点击标签时触发", "label": ""}], "tpl": "<t-check-tag>check tag</t-check-tag>", "path": "./check-tag/check-tag"}, "t-checkbox-group": {"key": "t-checkbox-group", "label": "多选框组", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-checkbox.png", "properties": [{"key": "disabled", "type": ["Boolean"], "desc": "是否禁用组件", "label": ""}, {"key": "max", "type": ["Number"], "desc": "支持最多选中的数量", "label": ""}, {"key": "name", "type": ["String"], "desc": "统一设置内部复选框 HTML 属性", "label": ""}, {"key": "options", "type": ["Array"], "desc": "以配置形式设置子元素。示例1：`['北京', '上海']` ，示例2: `[{ label: '全选', checkAll: true }, { label: '上海', value: 'shanghai' }]`。checkAll 值为 true 表示当前选项为「全选选项」", "label": ""}, {"key": "value", "type": ["Array"], "desc": "选中值", "label": ""}], "events": [{"key": "bind:change", "desc": "值变化时触发。`context.current` 表示当前变化的数据项，如果是全选则为空；`context.type` 表示引起选中数据变化的是选中或是取消选中，`context.option` 表示当前变化的数据项", "label": ""}], "tpl": "<t-checkbox-group value=\"{{['checkbox1']}}\"><t-checkbox value=\"checkbox1\" label=\"多选\" /><t-checkbox value=\"checkbox2\" label=\"多选\" /><t-checkbox value=\"checkbox3\" label=\"多选\" /></t-checkbox-group>", "require": {"t-checkbox": "./checkbox/checkbox"}, "path": "./checkbox-group/checkbox-group"}, "t-checkbox": {"key": "t-checkbox", "label": "多选框", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-checkbox.png", "properties": [{"key": "align", "type": ["String"], "desc": "多选框和内容相对位置", "label": ""}, {"key": "checkAll", "type": ["Boolean"], "desc": "用于标识是否为「全选选项」。单独使用无效，需在 CheckboxGroup 中使用", "label": ""}, {"key": "checked", "type": ["Boolean"], "desc": "是否选中", "label": ""}, {"key": "color", "type": ["String"], "desc": "多选框颜色", "label": ""}, {"key": "content", "type": ["String"], "desc": "多选框内容", "label": ""}, {"key": "contentDisabled", "type": ["Boolean"], "desc": "是否禁用组件内容（content）触发选中", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "是否禁用组件", "label": ""}, {"key": "icon", "type": ["Array"], "desc": "自定义选中图标和非选中图标。示例：[选中态图标地址，非选中态图标地址]", "label": ""}, {"key": "indeterminate", "type": ["Boolean"], "desc": "是否为半选", "label": ""}, {"key": "label", "type": ["String"], "desc": "主文案", "label": ""}, {"key": "maxContentRow", "type": ["Number"], "desc": "内容最大行数限制", "label": ""}, {"key": "maxLabelRow", "type": ["Number"], "desc": "主文案最大行数限制", "label": ""}, {"key": "name", "type": ["String"], "desc": "HTML 元素原生属性", "label": ""}, {"key": "readonly", "type": ["Boolean"], "desc": "只读状态", "label": ""}, {"key": "value", "type": ["String", "Number"], "desc": "多选框的值", "label": ""}], "externalClasses": ["t-class", "t-class-icon", "t-class-label", "t-class-content", "t-class-border"], "events": [{"key": "bind:change", "desc": "值变化时触发", "label": ""}], "tpl": "<t-checkbox label=\"多选框\" checked=\"{{true}}\"></t-checkbox>", "path": "./checkbox/checkbox"}, "t-collapse-panel": {"key": "t-collapse-panel", "label": "折叠面板", "icon": "", "properties": [{"key": "content", "type": ["String"], "desc": "折叠面板内容", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "禁止当前面板展开，优先级大于 Collapse 的同名属性", "label": ""}, {"key": "expandIcon", "type": ["Boolean"], "desc": "当前折叠面板展开图标，优先级大于 Collapse 的同名属性", "label": ""}, {"key": "header", "type": ["String"], "desc": "面板头内容", "label": ""}, {"key": "headerRightContent", "type": ["String"], "desc": "面板头的右侧区域，一般用于呈现面板操作", "label": ""}, {"key": "value", "type": ["String", "Number"], "desc": "当前面板唯一标识，如果值为空则取当前面下标兜底作为唯一标识", "label": ""}], "tpl": "<t-collapse-panel header=\"折叠面板标题\" value=\"{{0}}\">此处可自定义内容</t-collapse-panel>", "path": "./collapse/collapse-panel"}, "t-collapse": {"key": "t-collapse", "label": "折叠", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-collapse.png", "properties": [{"key": "defaultExpandAll", "type": ["Boolean"], "desc": "默认是否展开全部", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "是否禁用面板展开/收起操作", "label": ""}, {"key": "expandIcon", "type": ["Boolean"], "desc": "展开图标。值为 undefined 或 false 则不显示展开图标；值为 true 显示默认图标；值类型为函数，则表示完全自定义展开图标", "label": ""}, {"key": "expandMutex", "type": ["Boolean"], "desc": "每个面板互斥展开，每次只展开一个面板", "label": ""}, {"key": "value", "type": ["Array"], "desc": "展开的面板集合", "label": ""}], "events": [{"key": "bind:change", "desc": "切换面板时触发，返回变化的值", "label": ""}], "tpl": "<t-collapse value=\"{{[0]}}\"><t-collapse-panel header=\"折叠面板标题\" value=\"{{0}}\">此处可自定义内容</t-collapse-panel></t-collapse>", "require": {"t-collapse-panel": "./collapse/collapse-panel"}, "path": "./collapse/collapse"}, "t-date-time-picker": {"key": "t-date-time-picker", "label": "日期时间选择器", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-datetimepicker.png", "properties": [{"key": "cancelBtn", "type": ["String"], "desc": "取消按钮文字", "label": ""}, {"key": "confirmBtn", "type": ["String"], "desc": "确定按钮文字", "label": ""}, {"key": "end", "type": ["String", "Number"], "desc": "选择器的结束时间", "label": ""}, {"key": "footer", "type": ["String"], "desc": "底部内容", "label": ""}, {"key": "format", "type": ["String"], "desc": "用于格式化日期，[详细文档](https://day.js.org/docs/en/display/format)", "label": ""}, {"key": "header", "type": ["Boolean"], "desc": "头部内容。值为 true 显示空白头部，值为 false 不显示任何内容，值类型为 TNode 表示自定义头部内容", "label": ""}, {"key": "mode", "type": ["String", "Array"], "desc": "选择器模式，用于表示可以选择到哪一个层级。【示例一】year 或者 ['year'] 表示纯日期选择器，只能选择到年份，只显示年份。【示例二】'hour' 或 ['hour'] 表示纯时间选择器，只能选择到小时维度。【示例三】['year', 'month', 'date', 'hour', 'minute'] 表示，日期和时间 混合选择器，可以选择到具体哪一分钟，显示全部时间：年/月/日/时/分", "label": ""}, {"key": "showWeek", "type": ["Boolean"], "desc": "【开发中】是否在日期旁边显示周几（如周一，周二，周日等）", "label": ""}, {"key": "start", "type": ["String", "Number"], "desc": "选择器的开始时间", "label": ""}, {"key": "title", "type": ["String"], "desc": "标题", "label": ""}, {"key": "value", "type": ["String", "Number"], "desc": "选中值", "label": ""}, {"key": "visible", "type": ["Boolean"], "desc": "是否显示", "label": ""}], "externalClasses": ["t-class", "t-class-confirm", "t-class-cancel", "t-class-title"], "events": [{"key": "bind:cancel", "desc": "取消按钮点击时触发", "label": ""}, {"key": "bind:change", "desc": "选中值发生变化时触发", "label": ""}, {"key": "bind:column-change", "desc": "每一列选中数据变化时触发", "label": ""}, {"key": "bind:confirm", "desc": "确认按钮点击时触发", "label": ""}], "tpl": "<t-date-time-picker title=\"选择日期\" visible=\"{{true}}\" mode=\"{{['date']}}\" format=\"YYYY-MM-DD\"></t-date-time-picker>", "path": "./date-time-picker/date-time-picker"}, "t-dialog": {"key": "t-dialog", "label": "对话框", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-dialog.png", "properties": [{"key": "actions", "type": ["Array"], "desc": "操作栏", "label": ""}, {"key": "buttonLayout", "type": ["String"], "desc": "多按钮排列方式", "label": ""}, {"key": "cancelBtn", "type": ["String", "Object"], "desc": "取消按钮，可自定义。值为 null 则不显示取消按钮。值类型为字符串，则表示自定义按钮文本，值类型为 Object 则表示透传 Button 组件属性。使用 TNode 自定义按钮时，需自行控制取消事件", "label": ""}, {"key": "closeOnOverlayClick", "type": ["Boolean"], "desc": "点击蒙层时是否触发关闭事件", "label": ""}, {"key": "confirmBtn", "type": ["String", "Object"], "desc": "确认按钮。值为 null 则不显示确认按钮。值类型为字符串，则表示自定义按钮文本，值类型为 Object 则表示透传 Button 组件属性。使用 TNode 自定义按钮时，需自行控制确认事件", "label": ""}, {"key": "content", "type": ["String"], "desc": "内容", "label": ""}, {"key": "preventScrollThrough", "type": ["Boolean"], "desc": "防止滚动穿透", "label": ""}, {"key": "showInAttachedElement", "type": ["Boolean"], "desc": "【开发中】仅在挂载元素中显示抽屉，默认在浏览器可视区域显示。父元素需要有定位属性，如：position: relative", "label": ""}, {"key": "showOverlay", "type": ["Boolean"], "desc": "是否显示遮罩层", "label": ""}, {"key": "title", "type": ["String"], "desc": "标题", "label": ""}, {"key": "visible", "type": ["Boolean"], "desc": "控制对话框是否显示", "label": ""}, {"key": "zIndex", "type": ["Number"], "desc": "对话框层级，Web 侧样式默认为 2500，移动端和小程序样式默认为 1500", "label": ""}], "externalClasses": ["t-class", "t-class-content", "t-class-confirm", "t-class-cancel"], "events": [{"key": "bind:cancel", "desc": "如果“取消”按钮存在，则点击“取消”按钮时触发，同时触发关闭事件", "label": ""}, {"key": "bind:close", "desc": "关闭事件，点击 取消按钮 或 点击蒙层 时触发", "label": ""}, {"key": "bind:overlay-click", "desc": "如果蒙层存在，点击蒙层时触发", "label": ""}], "tpl": "<t-dialog visible=\"{{true}}\" title=\"对话框标题\" content=\"告知当前状态、信息和解决方法，等内容。描述文案尽可能控制在三行内\" confirm-btn=\"我知道了\" />", "path": "./dialog/dialog"}, "t-divider": {"key": "t-divider", "label": "分割线", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-divider.png", "properties": [{"key": "align", "type": ["String"], "desc": "文本位置（仅在水平分割线有效）", "label": ""}, {"key": "content", "type": ["String"], "desc": "子元素", "label": ""}, {"key": "dashed", "type": ["Boolean"], "desc": "是否虚线（仅在水平分割线有效）", "label": ""}, {"key": "layout", "type": ["String"], "desc": "分隔线类型有两种：水平和垂直", "label": ""}, {"key": "lineColor", "type": ["String"], "desc": "分隔线颜色", "label": ""}], "externalClasses": ["t-class", "t-class-line", "t-class-content"], "tpl": "<t-divider />", "path": "./divider/divider"}, "t-drawer": {"key": "t-drawer", "label": "模态抽屉", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-drawer.png", "properties": [{"key": "closeOnOverlayClick", "type": ["Boolean"], "desc": "点击蒙层时是否触发抽屉关闭事件", "label": ""}, {"key": "destroyOnClose", "type": ["Boolean"], "desc": "抽屉关闭时是否销毁节点", "label": ""}, {"key": "items", "type": ["Array"], "desc": "抽屉里的列表项", "label": ""}, {"key": "placement", "type": ["String"], "desc": "抽屉方向", "label": ""}, {"key": "showOverlay", "type": ["Boolean"], "desc": "是否显示遮罩层", "label": ""}, {"key": "visible", "type": ["Boolean"], "desc": "组件是否可见", "label": ""}, {"key": "zIndex", "type": ["Number"], "desc": "抽屉层级，样式默认为 1500", "label": ""}], "events": [{"key": "bind:close", "desc": "关闭事件，取消按钮点击时、关闭按钮点击时、点击蒙层时均会触发", "label": ""}, {"key": "bind:item-click", "desc": "点击抽屉里的列表项", "label": ""}, {"key": "bind:overlay-click", "desc": "如果蒙层存在，点击蒙层时触发", "label": ""}], "tpl": "<t-drawer visible=\"{{true}}\" placement=\"left\" items=\"{{[{title: '菜单一'},{title: '菜单二'}]}}\"></t-drawer>", "path": "./drawer/drawer"}, "t-dropdown-item": {"key": "t-dropdown-item", "label": "下拉菜单子项", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-dropdownmenu.png", "properties": [{"key": "disabled", "type": ["Boolean"], "desc": "是否禁用", "label": ""}, {"key": "label", "type": ["String"], "desc": "标题", "label": ""}, {"key": "multiple", "type": ["Boolean"], "desc": "是否多选", "label": ""}, {"key": "options", "type": ["Array"], "desc": "选项数据", "label": ""}], "tpl": "<t-dropdown-item label=\"菜单\" options=\"{{[{label:'选项一',value:'option_1',disabled:false},{label:'选项二',value:'option_2',disabled:false},{label:'选项三',value:'option_3',disabled:false}]}}\" defaultValue=\"option_3\" /><t-dropdown-item label=\"菜单\" options=\"{{[{label:'选项一',value:'option_1',disabled:false},{label:'选项二',value:'option_2',disabled:false},{label:'选项三',value:'option_3',disabled:false}]}}\" defaultValue=\"option_3\" />", "path": "./dropdown-menu/dropdown-item"}, "t-dropdown-menu": {"key": "t-dropdown-menu", "label": "下拉菜单", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-dropdownmenu.png", "properties": [{"key": "activeColor", "type": ["String"], "desc": "【讨论中】菜单标题和选项的选中态颜色", "label": ""}, {"key": "closeOnClickOverlay", "type": ["Boolean"], "desc": "是否在点击遮罩层后关闭菜单", "label": ""}, {"key": "duration", "type": ["String", "Number"], "desc": "动画时长", "label": ""}, {"key": "showOverlay", "type": ["Boolean"], "desc": "是否显示遮罩层", "label": ""}, {"key": "zIndex", "type": ["Number"], "desc": "菜单栏 z-index 层级", "label": ""}], "tpl": "<t-dropdown-menu><t-dropdown-item label=\"菜单\" options=\"{{[{label:'选项一',value:'option_1',disabled:false},{label:'选项二',value:'option_2',disabled:false},{label:'选项三',value:'option_3',disabled:false}]}}\" defaultValue=\"option_3\" /><t-dropdown-item label=\"菜单\" options=\"{{[{label:'选项一',value:'option_1',disabled:false},{label:'选项二',value:'option_2',disabled:false},{label:'选项三',value:'option_3',disabled:false}]}}\" defaultValue=\"option_3\" /></t-dropdown-menu>", "require": {"t-dropdown-item": "./dropdown-menu/dropdown-item"}, "path": "./dropdown-menu/dropdown-menu"}, "t-empty": {"key": "t-empty", "label": "空状态", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-empty.png", "properties": [{"key": "action", "type": ["String"], "desc": "操作按钮", "label": ""}, {"key": "description", "type": ["String"], "desc": "描述文字", "label": ""}, {"key": "icon", "type": ["String"], "desc": "图标名称", "label": ""}, {"key": "image", "type": ["String"], "desc": "图片地址", "label": ""}], "externalClasses": ["t-class", "t-class-description", "t-class-image", "t-class-actions"], "tpl": "<t-empty icon=\"info-circle-filled\" />", "path": "./empty/empty"}, "t-fab": {"key": "t-fab", "label": "悬浮按钮", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-fab.png", "properties": [{"key": "buttonProps", "type": ["Object"], "desc": "透传至 Button 组件", "label": ""}, {"key": "icon", "type": ["String"], "desc": "图标", "label": ""}, {"key": "style", "type": ["String"], "desc": "悬浮按钮的样式，常用于调整位置", "label": ""}, {"key": "text", "type": ["String"], "desc": "文本内容", "label": ""}], "events": [{"key": "bind:click", "desc": "悬浮按钮点击事件", "label": ""}], "tpl": "<t-fab icon=\"add\" />", "path": "./fab/fab"}, "t-footer": {"key": "t-footer", "label": "布局-底部内容", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-footer.png", "properties": [{"key": "copyright", "type": ["String"], "desc": "版权信息，type 为`text`生效", "label": ""}, {"key": "logo", "type": ["Object"], "desc": "图标配置，type 为`logo`生效。`logo.icon` 表示图标链接地址，`logo.title` 表示标题文本，`logo.url` 表示链接跳转地址", "label": ""}, {"key": "textLinkList", "type": ["Array"], "desc": "链接列表，type 为`text`生效。name 表示链接名称， url 表示链接 page 路径，目前只支持小程序内部跳转，openType 表示跳转方式", "label": ""}, {"key": "theme", "type": ["String"], "desc": "页脚展示类型", "label": ""}], "tpl": "<t-footer theme=\"text\" copyright=\"Copyright © 2021-2031 TD.All Rights Reserved.\" />", "path": "./footer/footer"}, "t-grid-item": {"key": "t-grid-item", "label": "宫格子项", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-grid.png", "properties": [{"key": "badgeProps", "type": ["Object"], "desc": "头像右上角提示信息，继承 Badge 组件的全部特性。如：小红点，或者数字", "label": ""}, {"key": "description", "type": ["String"], "desc": "文本以外的更多描述，辅助信息。可以通过 Props 传入文本，也可以自定义标题节点", "label": ""}, {"key": "image", "type": ["String"], "desc": "图片，可以是图片地址，也可以自定义图片节点", "label": ""}, {"key": "jumpType", "type": ["String"], "desc": "链接跳转类型", "label": ""}, {"key": "layout", "type": ["String"], "desc": "内容布局方式", "label": ""}, {"key": "text", "type": ["String"], "desc": "文本，可以通过 Props 传入文本，也可以自定义标题节点", "label": ""}, {"key": "url", "type": ["String"], "desc": "点击后的跳转链接", "label": ""}], "externalClasses": ["t-class", "t-class-image", "t-class-text", "t-class-description"], "tpl": "<t-grid-item text=\"标题文字\"><image style=\"width: 96rpx; height: 96rpx\" src=\"https://tdesign.gtimg.com/mobile/%E5%9B%BE%E7%89%87.png\" slot=\"image\" /></t-grid-item>", "path": "./grid-item/grid-item"}, "t-grid": {"key": "t-grid", "label": "栅格", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-grid.png", "properties": [{"key": "align", "type": ["String"], "desc": "内容对齐方式", "label": ""}, {"key": "border", "type": ["Boolean", "Object"], "desc": "边框，默认不显示。值为 true 则显示默认边框，值类型为 object 则表示自定义边框样式", "label": ""}, {"key": "column", "type": ["Number"], "desc": "每一行的列数量", "label": ""}, {"key": "gutter", "type": ["Number"], "desc": "间隔大小", "label": ""}, {"key": "hover", "type": ["Boolean"], "desc": "是否开启点击反馈", "label": ""}], "externalClasses": ["t-class"], "tpl": "<t-grid column=\"{{2}}\"><t-grid-item text=\"标题文字\"><image style=\"width: 96rpx; height: 96rpx\" src=\"https://tdesign.gtimg.com/mobile/%E5%9B%BE%E7%89%87.png\" slot=\"image\" /></t-grid-item><t-grid-item text=\"标题文字\"><image style=\"width: 96rpx; height: 96rpx\" src=\"https://tdesign.gtimg.com/mobile/%E5%9B%BE%E7%89%87.png\" slot=\"image\" /></t-grid-item></t-grid>", "require": {"t-grid-item": "./grid-item/grid-item"}, "path": "./grid/grid"}, "t-icon": {"key": "t-icon", "label": "图标", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-icon.png", "properties": [{"key": "classPrefix", "type": ["String"], "desc": "自定义icon前缀", "label": ""}, {"key": "color", "type": ["String"], "desc": "图标颜色", "label": ""}, {"key": "style", "type": ["String"], "desc": "自定义样式", "label": ""}, {"key": "name", "type": ["String"], "desc": "图标名称", "label": ""}, {"key": "size", "type": ["String", "Number"], "desc": "图标名称", "label": ""}], "tpl": "<t-icon name=\"add-circle\" />", "path": "./icon/icon"}, "t-image": {"key": "t-image", "label": "图片", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-image.png", "properties": [{"key": "error", "type": ["String"], "desc": "加载失败时显示的内容。值为 `default` 则表示使用默认加载失败风格；值为空或者 `slot` 表示使用插槽渲染，插槽名称为 `error`；值为其他则表示普通文本内容，如“加载失败”", "label": ""}, {"key": "lazy", "type": ["Boolean"], "desc": "是否开启图片懒加载", "label": ""}, {"key": "loading", "type": ["String"], "desc": "加载态内容。值为 `default` 则表示使用默认加载中风格；值为空或者 `slot` 表示使用插槽渲染，插槽名称为 `loading`；值为其他则表示普通文本内容，如“加载中”", "label": ""}, {"key": "shape", "type": ["String"], "desc": "图片圆角类型", "label": ""}, {"key": "src", "type": ["String"], "desc": "图片链接", "label": ""}], "externalClasses": ["t-class", "t-class-load"], "events": [{"key": "bind:error", "desc": "图片加载失败时触发", "label": ""}, {"key": "bind:load", "desc": "图片加载完成时触发", "label": ""}], "tpl": "<t-image src=\"https://tdesign.gtimg.com/mobile/%E5%9B%BE%E7%89%87.png\" mode=\"aspectFill\"></t-image>", "path": "./image/image"}, "t-indexes": {"key": "t-indexes", "label": "索引", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-indexes.png", "properties": [{"key": "height", "type": ["Number"], "desc": "列表高度，未设置默认占满设备高度", "label": ""}, {"key": "list", "type": ["Array"], "desc": "索引列表的列表数据。每个元素包含三个子元素，index(string)：索引值，例如1，2，3，...或A，B，C等；title(string): 索引标题，可不填将默认设为索引值；children(Array<{title: string}>): 子元素列表，title为子元素的展示文案。", "label": ""}, {"key": "sticky", "type": ["Boolean"], "desc": "索引是否吸顶，默认为true", "label": ""}], "events": [{"key": "bind:select", "desc": "点击行元素时触发事件", "label": ""}], "tpl": "<t-indexes id=\"bar\" list=\"{{ [{ title: 'A开头', index: 'A', children: [{ title: '阿坝' }]},{ title: 'B开头', index: 'B', children: [{ title: '北京' }]}] }}\" />", "path": "./indexes/indexes"}, "t-input": {"key": "t-input", "label": "输入框", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-input.png", "properties": [{"key": "align", "type": ["String"], "desc": "文本内容位置，居左/居中/居右", "label": ""}, {"key": "borderless", "type": ["Boolean"], "desc": "【讨论中】是否开启无边框模式", "label": ""}, {"key": "clearable", "type": ["Boolean"], "desc": "是否可清空", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "是否禁用输入框", "label": ""}, {"key": "errorMessage", "type": ["String"], "desc": "错误提示文本，值为空不显示（废弃属性，如果需要，请更为使用 status 和 tips）", "label": ""}, {"key": "format", "type": ["String"], "desc": "【开发中】指定输入框展示值的格式", "label": ""}, {"key": "label", "type": ["String"], "desc": "左侧文本", "label": ""}, {"key": "max<PERSON><PERSON>ter", "type": ["Number"], "desc": "用户最多可以输入的字符个数，一个中文汉字表示两个字符长度。`maxcharacter` 和 `maxlength` 二选一使用", "label": ""}, {"key": "maxlength", "type": ["Number"], "desc": "用户最多可以输入的文本长度，一个中文等于一个计数长度。值小于等于 0 的时候，则表示不限制输入长度。`maxcharacter` 和 `maxlength` 二选一使用", "label": ""}, {"key": "placeholder", "type": ["String"], "desc": "占位符", "label": ""}, {"key": "prefixIcon", "type": ["String"], "desc": "组件前置图标，值为字符串则表示图标名称", "label": ""}, {"key": "readonly", "type": ["Boolean"], "desc": "只读状态", "label": ""}, {"key": "size", "type": ["String"], "desc": "输入框尺寸", "label": ""}, {"key": "status", "type": ["String"], "desc": "输入框状态", "label": ""}, {"key": "suffix", "type": ["String"], "desc": "后置图标前的后置内容", "label": ""}, {"key": "suffixIcon", "type": ["String"], "desc": "后置文本内容，值为字符串则表示图标名称", "label": ""}, {"key": "tips", "type": ["String"], "desc": "输入框下方提示文本，会根据不同的 `status` 呈现不同的样式", "label": ""}, {"key": "value", "type": ["String", "Number"], "desc": "输入框的值", "label": ""}], "externalClasses": ["t-class", "t-class-input", "t-class-placeholder", "t-class-error-msg"], "events": [{"key": "bind:blur", "desc": "失去焦点时触发", "label": ""}, {"key": "bind:change", "desc": "输入框值发生变化时触发", "label": ""}, {"key": "bind:clear", "desc": "清空按钮点击时触发", "label": ""}, {"key": "bind:enter", "desc": "回车键按下时触发", "label": ""}, {"key": "bind:focus", "desc": "获得焦点时触发", "label": ""}], "tpl": "<t-input placeholder=\"请输入文字\" />", "path": "./input/input"}, "t-loading": {"key": "t-loading", "label": "加载中", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-loading.png", "properties": [{"key": "delay", "type": ["Number"], "desc": "延迟显示加载效果的时间，用于防止请求速度过快引起的加载闪烁，单位：毫秒", "label": ""}, {"key": "duration", "type": ["Number"], "desc": "加载动画执行完成一次的时间，单位：毫秒", "label": ""}, {"key": "indicator", "type": ["Boolean"], "desc": "是否显示加载指示符", "label": ""}, {"key": "inheritColor", "type": ["Boolean"], "desc": "是否继承父元素颜色", "label": ""}, {"key": "layout", "type": ["String"], "desc": "对齐方式", "label": ""}, {"key": "loading", "type": ["Boolean"], "desc": "是否处于加载状态", "label": ""}, {"key": "pause", "type": ["Boolean"], "desc": "是否暂停动画", "label": ""}, {"key": "progress", "type": ["Number"], "desc": "加载进度", "label": ""}, {"key": "reverse", "type": ["Boolean"], "desc": "加载动画是否反向", "label": ""}, {"key": "size", "type": ["String"], "desc": "尺寸，示例：40rpx/20px", "label": ""}, {"key": "text", "type": ["String"], "desc": "加载提示文案", "label": ""}, {"key": "theme", "type": ["String"], "desc": "加载组件类型", "label": ""}], "externalClasses": ["t-class", "t-class-text", "t-class-indicator"], "tpl": "<t-loading theme=\"circular\" size=\"40rpx\"></t-loading>", "path": "./loading/loading"}, "t-message": {"key": "t-message", "label": "全局提醒", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-message.png", "properties": [{"key": "action", "type": ["String"], "desc": "操作", "label": ""}, {"key": "align", "type": ["String"], "desc": "文本对齐方式", "label": ""}, {"key": "closeBtn", "type": ["String", "Boolean"], "desc": "关闭按钮，可以自定义。值为 true 显示默认关闭按钮，值为 false 不显示关闭按钮。值类型为 string 则直接显示值，如：“关闭”。也可以完全自定义按钮", "label": ""}, {"key": "content", "type": ["String"], "desc": "用于自定义消息弹出内容", "label": ""}, {"key": "duration", "type": ["Number"], "desc": "消息内置计时器，计时到达时会触发 duration-end 事件。单位：毫秒。值为 0 则表示没有计时器。", "label": ""}, {"key": "icon", "type": ["String", "Boolean"], "desc": "消息提醒前面的图标。值为 true 则根据 theme 显示对应的图标，值为 false 则不显示图标。值为 'info' 或 'bell' 则显示组件内置图标。也可以完全自定义图标节点", "label": ""}, {"key": "marquee", "type": ["Boolean", "Object"], "desc": "跑马灯效果。speed 指速度控制；loop 指循环播放次数，值为 -1 表示循环播放，值为 0 表示不循环播放；delay 表示延迟多久开始播放", "label": ""}, {"key": "offset", "type": ["Array"], "desc": "相对于 placement 的偏移量，示例：[-10, 20] 或 ['10rpx', '8rpx']", "label": ""}, {"key": "theme", "type": ["String"], "desc": "消息组件风格", "label": ""}, {"key": "visible", "type": ["Boolean"], "desc": "是否显示，隐藏时默认销毁组件", "label": ""}, {"key": "zIndex", "type": ["Number"], "desc": "元素层级，样式默认为 5000", "label": ""}], "externalClasses": ["t-class", "t-class-content", "t-class-icon", "t-class-action", "t-class-close-btn"], "events": [{"key": "bind:action-btn-click", "desc": "当操作按钮存在时，用户点击操作按钮时触发", "label": ""}, {"key": "bind:close-btn-click", "desc": "当关闭按钮存在时，用户点击关闭按钮触发", "label": ""}, {"key": "bind:duration-end", "desc": "计时结束后触发", "label": ""}], "tpl": "<t-message id=\"t-message\" />", "path": "./message/message"}, "t-navbar": {"key": "t-navbar", "label": "导航条", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-navbar.png", "properties": [{"key": "animation", "type": ["Boolean"], "desc": "是否添加动画效果", "label": ""}, {"key": "background", "type": ["String"], "desc": "背景", "label": ""}, {"key": "delta", "type": ["Number"], "desc": "后退按钮后退层数，含义参考 [wx.navigateBack](https://developers.weixin.qq.com/miniprogram/dev/api/route/wx.navigateBack.html)，特殊的，传入 0 不会发生执行 wx.navigateBack，只会触发一个 goback 事件供自行处理。", "label": ""}, {"key": "fixed", "type": ["Boolean"], "desc": "是否固定在顶部", "label": ""}, {"key": "homeIcon", "type": ["String"], "desc": "首页图标地址。值为 '' 或者 undefiend 则表示不显示返回图标，值为 'circle' 表示显示默认图标，值为 'slot' 表示使用插槽渲染，值为其他则表示图标地址", "label": ""}, {"key": "leftIcon", "type": ["String"], "desc": "左侧图标地址，值为 '' 或者 undefiend 则表示不显示返回图标，值为 'arrow-left' 表示显示返回图标，值为 'slot' 表示使用插槽渲染，值为其他则表示图标地址", "label": ""}, {"key": "title", "type": ["String"], "desc": "页面标题", "label": ""}, {"key": "titleMaxLength", "type": ["Number"], "desc": "标题文字最大长度，超出的范围使用 `...` 表示", "label": ""}, {"key": "visible", "type": ["Boolean"], "desc": "是否显示", "label": ""}], "externalClasses": ["t-class", "t-class-title", "t-class-left-icon", "t-class-home-icon", "t-class-capsule"], "events": [{"key": "bind:complete", "desc": "navigateBack 执行完成后触发（失败或成功均会触发）", "label": ""}, {"key": "bind:fail", "desc": "navigateBack 执行失败后触发", "label": ""}, {"key": "bind:go-back", "desc": "delta 值为 0 时，点击返回，触发该事件", "label": ""}, {"key": "bind:go-home", "desc": "点击 Home 触发", "label": ""}, {"key": "bind:success", "desc": "navigateBack 执行成功后触发", "label": ""}], "tpl": "<t-navbar title=\"标题\" />", "path": "./navbar/navbar"}, "t-picker-item": {"key": "t-picker-item", "label": "选择器子项", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-picker.png", "properties": [{"key": "format", "type": ["String"], "desc": "格式化标签", "label": ""}, {"key": "options", "type": ["Array"], "desc": "数据源", "label": ""}], "tpl": "<t-picker-item options=\"{{[{ label: '上海', value: '上海' },{ label: '广州', value: '广州' },{ label: '深圳', value: '深圳' }]}}\" value=\"广州\"></t-picker-item>", "path": "./picker-item/picker-item"}, "t-picker": {"key": "t-picker", "label": "选择器", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-picker.png", "properties": [{"key": "cancelBtn", "type": ["String", "Object"], "desc": "取消按钮文字", "label": ""}, {"key": "confirmBtn", "type": ["String", "Object"], "desc": "确定按钮文字", "label": ""}, {"key": "footer", "type": ["String"], "desc": "底部内容", "label": ""}, {"key": "header", "type": ["Boolean"], "desc": "头部内容。值为 true 显示空白头部，值为 false 不显示任何内容，值类型为 TNode 表示自定义头部内容", "label": ""}, {"key": "title", "type": ["String"], "desc": "标题", "label": ""}, {"key": "value", "type": ["Array"], "desc": "选中值", "label": ""}, {"key": "visible", "type": ["Boolean"], "desc": "是否显示", "label": ""}], "events": [{"key": "bind:cancel", "desc": "点击取消按钮时触发", "label": ""}, {"key": "bind:change", "desc": "选中变化时候触发", "label": ""}, {"key": "bind:pick", "desc": "任何一列选中都会触发，不同的列参数不同。`context.column` 表示第几列变化，`context.index` 表示变化那一列的选中项下标", "label": ""}], "tpl": "<t-picker visible=\"{{true}}\" cancelBtn=\"取消\" confirmBtn=\"确认\"><t-picker-item options=\"{{[{ label: '上海', value: '上海' },{ label: '广州', value: '广州' },{ label: '深圳', value: '深圳' }]}}\" value=\"广州\"></t-picker-item></t-picker>", "require": {"t-picker-item": "./picker-item/picker-item"}, "path": "./picker/picker"}, "t-popup": {"key": "t-popup", "label": "气泡框", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-popup.png", "properties": [{"key": "closeBtn", "type": ["Boolean"], "desc": "关闭按钮，值类型为 Boolean 时表示是否显示关闭按钮。也可以自定义关闭按钮", "label": ""}, {"key": "closeOnOverlayClick", "type": ["Boolean"], "desc": "点击遮罩层是否关闭", "label": ""}, {"key": "content", "type": ["String"], "desc": "浮层里面的内容", "label": ""}, {"key": "placement", "type": ["String"], "desc": "浮层出现位置", "label": ""}, {"key": "preventScrollThrough", "type": ["Boolean"], "desc": "防止滚动穿透", "label": ""}, {"key": "showOverlay", "type": ["Boolean"], "desc": "是否显示遮罩层", "label": ""}, {"key": "transitionProps", "type": ["Object"], "desc": "动画效果定义", "label": ""}, {"key": "visible", "type": ["Boolean"], "desc": "是否显示浮层", "label": ""}, {"key": "zIndex", "type": ["Number"], "desc": "组件层级，Web 侧样式默认为 5500，移动端和小程序样式默认为 1500", "label": ""}], "externalClasses": ["t-class", "t-class-overlay", "t-class-content"], "events": [{"key": "bind:visible-change", "desc": "当浮层隐藏或显示时触发", "label": ""}], "tpl": "<t-popup visible=\"{{true}}\" placement=\"top\"><view style=\"width: 100vw; height: 35vh; background: #fff\" /></t-popup>", "path": "./popup/popup"}, "t-progress": {"key": "t-progress", "label": "进度条", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-progress.png", "properties": [{"key": "color", "type": ["String", "Object", "Array"], "desc": "进度条颜色。示例：'#ED7B2F' 或 'orange' 或 `['#f00', '#0ff', '#f0f']` 或 `{ '0%': '#f00', '100%': '#0ff' }` 或  `{ from: '#000', to: '#000' }` 等", "label": ""}, {"key": "label", "type": ["String", "Boolean"], "desc": "进度百分比，可自定义", "label": ""}, {"key": "percentage", "type": ["Number"], "desc": "进度条百分比", "label": ""}, {"key": "size", "type": ["String", "Number"], "desc": "进度条尺寸，示例：small/medium/large/240。small 值为 72； medium 值为 112；large 值为 160", "label": ""}, {"key": "status", "type": ["String"], "desc": "进度条状态", "label": ""}, {"key": "strokeWidth", "type": ["String", "Number"], "desc": "进度条线宽。宽度数值不能超过 size 的一半，否则不能输出环形进度", "label": ""}, {"key": "theme", "type": ["String"], "desc": "进度条风格。值为 line，标签（label）显示在进度条右侧；值为 plump，标签（label）显示在进度条里面；值为 circle，标签（label）显示在进度条正中间", "label": ""}, {"key": "trackColor", "type": ["String"], "desc": "进度条未完成部分颜色", "label": ""}], "tpl": "<t-progress></t-progress>", "path": "./progress/progress"}, "t-pull-down-refresh": {"key": "t-pull-down-refresh", "label": "下拉刷新", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-pulldownrefresh.png", "properties": [{"key": "loadingBarHeight", "type": ["String", "Number"], "desc": "加载中下拉高度，如果值为数字则单位是：'px'", "label": ""}, {"key": "loadingProps", "type": ["Object"], "desc": "加载loading样式", "label": ""}, {"key": "loadingTexts", "type": ["Array"], "desc": "提示语，组件内部默认值为 ['下拉刷新', '松手刷新', '正在刷新', '刷新完成']", "label": ""}, {"key": "maxBarHeight", "type": ["String", "Number"], "desc": "最大下拉高度，如果值为数字则单位是：'px'", "label": ""}, {"key": "refreshTimeout", "type": ["Number"], "desc": "刷新超时时间", "label": ""}, {"key": "value", "type": ["Boolean"], "desc": "组件状态，值为 `true` 表示下拉状态，值为 `false` 表示收起状态", "label": ""}], "externalClasses": ["t-class", "t-class-loading", "t-class-text", "t-class-indicator"], "events": [{"key": "bind:change", "desc": "下拉或收起时触发，用户手势往下滑动触发下拉状态，手势松开触发收起状态", "label": ""}, {"key": "bind:refresh", "desc": "结束下拉时触发", "label": ""}, {"key": "bind:timeout", "desc": "刷新超时触发", "label": ""}], "tpl": "<t-pull-down-refresh id=\"pull-down-refresh\" loadingTexts=\"{{['继续拉哦', '该松手啦', '努力刷新中', '完成~']}}\"><view style=\"height: 300rpx; background: #fff; text-align: center\">拖拽该区域演示 中间下拉刷新</view></t-pull-down-refresh>", "path": "./pull-down-refresh/pull-down-refresh"}, "t-radio-group": {"key": "t-radio-group", "label": "单选框组", "icon": "", "properties": [{"key": "disabled", "type": ["Boolean"], "desc": "是否禁用全部子单选框", "label": ""}, {"key": "name", "type": ["String"], "desc": "HTML 元素原生属性", "label": ""}, {"key": "options", "type": ["Array"], "desc": "单选组件按钮形式。RadioOption 数据类型为 string 或 number 时，表示 label 和 value 值相同", "label": ""}, {"key": "value", "type": ["String", "Number", "Boolean"], "desc": "选中的值", "label": ""}], "events": [{"key": "bind:change", "desc": "选中值发生变化时触发", "label": ""}], "tpl": "<t-radio-group defaultValue=\"radio1\"><t-radio value=\"radio1\" label=\"单选\" /><t-radio value=\"radio2\" label=\"单选\" /></t-radio-group>", "require": {"t-radio": "./radio/radio"}, "path": "./radio-group/radio-group"}, "t-radio": {"key": "t-radio", "label": "单选框", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-radio.png", "properties": [{"key": "align", "type": ["String"], "desc": "复选框和内容相对位置", "label": ""}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "type": ["Boolean"], "desc": "是否允许取消选中", "label": ""}, {"key": "checked", "type": ["Boolean"], "desc": "是否选中", "label": ""}, {"key": "color", "type": ["String"], "desc": "单选按钮颜色", "label": ""}, {"key": "content", "type": ["String"], "desc": "单选内容", "label": ""}, {"key": "contentDisabled", "type": ["Boolean"], "desc": "是否禁用组件内容（content）触发选中", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "是否为禁用态", "label": ""}, {"key": "icon", "type": ["String", "Array"], "desc": "自定义选中图标和非选中图标。示例：[选中态图标，非选中态图标]。值为 fill-circle 表示图标为填充型图标，值为 stroke-line 表示图标为描边型图标", "label": ""}, {"key": "label", "type": ["String"], "desc": "主文案", "label": ""}, {"key": "maxContentRow", "type": ["Number"], "desc": "内容最大行数限制", "label": ""}, {"key": "maxLabelRow", "type": ["Number"], "desc": "主文案最大行数限制", "label": ""}, {"key": "name", "type": ["String"], "desc": "HTML 元素原生属性", "label": ""}, {"key": "value", "type": ["String", "Number", "Boolean"], "desc": "单选按钮的值", "label": ""}], "externalClasses": ["t-class", "t-class-icon", "t-class-label", "t-class-content", "t-class-border"], "events": [{"key": "bind:change", "desc": "值变化时触发", "label": ""}], "tpl": "<t-radio value=\"radio\" label=\"单选\" />", "path": "./radio/radio"}, "t-rate": {"key": "t-rate", "label": "评分", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-rate.png", "properties": [{"key": "allowHalf", "type": ["Boolean"], "desc": "是否允许半选", "label": ""}, {"key": "color", "type": ["String", "Array"], "desc": "评分图标的颜色，样式中默认为 #ED7B2F。一个值表示设置选中高亮的五角星颜色，示例：[选中颜色]。数组则表示分别设置 选中高亮的五角星颜色 和 未选中暗灰的五角星颜色，[选中颜色，未选中颜色]。示例：['#ED7B2F', '#E3E6EB']", "label": ""}, {"key": "count", "type": ["Number"], "desc": "评分的数量", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "是否禁用评分", "label": ""}, {"key": "gap", "type": ["Number"], "desc": "评分图标的间距", "label": ""}, {"key": "showText", "type": ["Boolean"], "desc": "是否显示对应的辅助文字", "label": ""}, {"key": "size", "type": ["String"], "desc": "评分图标的大小，示例：`20`", "label": ""}, {"key": "texts", "type": ["Array"], "desc": "评分等级对应的辅助文字。组件内置默认值为：['极差', '失望', '一般', '满意', '惊喜']。自定义值示例：['1分', '2分', '3分', '4分', '5分']", "label": ""}, {"key": "value", "type": ["Number"], "desc": "选择评分的值", "label": ""}, {"key": "variant", "type": ["String"], "desc": "形状类型，有描边类型和填充类型两种", "label": ""}], "events": [{"key": "bind:change", "desc": "评分数改变时触发", "label": ""}], "tpl": "<t-rate defaultValue=\"{{4}}\" variant=\"filled\"></t-rate>", "path": "./rate/rate"}, "t-search": {"key": "t-search", "label": "搜索", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-search.png", "properties": [{"key": "action", "type": ["String"], "desc": "自定义右侧操作按钮文字", "label": ""}, {"key": "center", "type": ["Boolean"], "desc": "是否居中", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "是否禁用", "label": ""}, {"key": "focus", "type": ["Boolean"], "desc": "是否聚焦", "label": ""}, {"key": "label", "type": ["String"], "desc": "左侧文本", "label": ""}, {"key": "leftIcon", "type": ["String"], "desc": "左侧图标", "label": ""}, {"key": "placeholder", "type": ["String"], "desc": "占位符", "label": ""}, {"key": "rightIcon", "type": ["String"], "desc": "右侧图标", "label": ""}, {"key": "shape", "type": ["String"], "desc": "搜索框形状", "label": ""}, {"key": "value", "type": ["String"], "desc": "值", "label": ""}], "externalClasses": ["t-class", "t-class-input", "t-class-input-container", "t-class-cancel", "t-class-left", "t-class-right"], "events": [{"key": "bind:action-click", "desc": "点击右侧操作按钮文字时触发", "label": ""}, {"key": "bind:blur", "desc": "失去焦点时触发", "label": ""}, {"key": "bind:change", "desc": "值发生变化时触发", "label": ""}, {"key": "bind:clear", "desc": "点击清除时触发", "label": ""}, {"key": "bind:focus", "desc": "聚焦时触发", "label": ""}, {"key": "bind:submit", "desc": "提交时触发", "label": ""}], "tpl": "<t-search></t-search>", "path": "./search/search"}, "t-skeleton": {"key": "t-skeleton", "label": "骨架屏", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-skeleton.png", "properties": [{"key": "animation", "type": ["String"], "desc": "动画效果，有「渐变加载动画」和「闪烁加载动画」两种。值为 'none' 则表示没有动画", "label": ""}, {"key": "delay", "type": ["Number"], "desc": "【开发中】延迟显示加载效果的时间，用于防止请求速度过快引起的加载闪烁，单位：毫秒", "label": ""}, {"key": "loading", "type": ["Boolean"], "desc": "是否为加载状态，如果是则显示骨架图，如果不是则显示加载完成的内容", "label": ""}, {"key": "rowCol", "type": ["Array"], "desc": "用于设置行列数量、宽度高度、间距等。【示例一】，`[1, 1, 2]` 表示输出三行骨架图，第一行一列，第二行一列，第三行两列。【示例二】，`[1, 1, { width: '100px' }]` 表示自定义第三行的宽度为 `100px`。【示例三】，`[1, 2, [{ width, height }, { width, height, marginLeft }]]` 表示第三行有两列，且自定义宽度、高度和间距", "label": ""}, {"key": "theme", "type": ["String"], "desc": "骨架图风格，有基础、头像组合等两大类", "label": ""}], "externalClasses": ["t-class", "t-class-avatar", "t-class-image", "t-class-text"], "tpl": "<t-skeleton rowCol=\"{{ [{ width: '686rpx', height: '32rpx' }, 1, 1, { width: '380rpx', height: '32rpx' }]}}\" loading></t-skeleton>", "path": "./skeleton/skeleton"}, "t-slider": {"key": "t-slider", "label": "滑块", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-slider.png", "properties": [{"key": "colors", "type": ["Array"], "desc": "颜色，[已选择, 未选择]", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "是否禁用组件", "label": ""}, {"key": "disabledColor", "type": ["Array"], "desc": "禁用状态滑动条的颜色，[已选, 未选]", "label": ""}, {"key": "label", "type": ["String", "Boolean"], "desc": "滑块当前值文本。<br />值为 true 显示默认文案；值为 false 不显示滑块当前值文本；<br />值为 `${value}%` 则表示组件会根据占位符渲染文案；<br />值类型为函数时，参数 `value` 标识滑块值，参数 `position=start` 表示范围滑块的起始值，参数 `position=end` 表示范围滑块的终点值", "label": ""}, {"key": "marks", "type": ["Object", "Array"], "desc": "刻度标记，示例：`[0, 10, 40, 200]` 或者 `{ 5:  '5¥', 10: '10%' }`", "label": ""}, {"key": "max", "type": ["Number"], "desc": "滑块范围最大值", "label": ""}, {"key": "min", "type": ["Number"], "desc": "滑块范围最小值", "label": ""}, {"key": "range", "type": ["Boolean"], "desc": "双游标滑块", "label": ""}, {"key": "showExtremeValue", "type": ["Boolean"], "desc": "是否边界值", "label": ""}, {"key": "step", "type": ["Number"], "desc": "步长", "label": ""}, {"key": "value", "type": ["Number", "Array"], "desc": "滑块值", "label": ""}], "externalClasses": ["t-class", "t-class-bar", "t-class-bar-active", "t-class-bar-disabled", "t-class-cursor"], "events": [{"key": "bind:change", "desc": "滑块值变化时触发", "label": ""}, {"key": "bind:dragend", "desc": "结束拖动时触发", "label": ""}, {"key": "bind:dragstart", "desc": "开始拖动时触发", "label": ""}], "tpl": "<t-slider />", "path": "./slider/slider"}, "t-step-item": {"key": "t-step-item", "label": "步骤", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-steps.png", "properties": [{"key": "content", "type": ["String"], "desc": "步骤描述", "label": ""}, {"key": "icon", "type": ["String"], "desc": "图标。传入 slot 代表使用插槽，其他字符串代表使用内置图标", "label": ""}, {"key": "status", "type": ["String"], "desc": "当前步骤的状态", "label": ""}, {"key": "subStepItems", "type": ["Array"], "desc": "子步骤条，仅支持 layout  = 'vertical' 时", "label": ""}, {"key": "title", "type": ["String"], "desc": "标题", "label": ""}], "externalClasses": ["t-class", "t-class-content", "t-class-title", "t-class-description", "t-class-extra"], "tpl": "<t-step-item title=\"步骤描述\"></t-step-item>", "path": "./step-item/step-item"}, "t-stepper": {"key": "t-stepper", "label": "步进器", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-stepper.png", "properties": [{"key": "disabled", "type": ["Boolean"], "desc": "禁用全部操作", "label": ""}, {"key": "disableInput", "type": ["Boolean"], "desc": "禁用输入框", "label": ""}, {"key": "inputWidth", "type": ["Number"], "desc": "输入框宽度", "label": ""}, {"key": "max", "type": ["Number"], "desc": "最大值", "label": ""}, {"key": "min", "type": ["Number"], "desc": "最小值", "label": ""}, {"key": "step", "type": ["Number"], "desc": "步长", "label": ""}, {"key": "theme", "type": ["String"], "desc": "组件风格", "label": ""}, {"key": "value", "type": ["String", "Number"], "desc": "值", "label": ""}], "externalClasses": ["t-class", "t-class-input", "t-class-add", "t-class-minus"], "events": [{"key": "bind:blur", "desc": "输入框失去焦点时触发", "label": ""}, {"key": "bind:change", "desc": "数值发生变更时触发", "label": ""}, {"key": "bind:overlimit", "desc": "数值超出限制时触发", "label": ""}], "tpl": "<t-stepper />", "path": "./stepper/stepper"}, "t-steps": {"key": "t-steps", "label": "步骤条", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-steps.png", "properties": [{"key": "current", "type": ["String", "Number"], "desc": "当前步骤，即整个步骤条进度。默认根据步骤下标判断步骤的完成状态，当前步骤为进行中，当前步骤之前的步骤为已完成，当前步骤之后的步骤为未开始。如果每个步骤没有设置 value，current 值为步骤长度则表示所有步骤已完成。如果每个步骤设置了自定义 value，则 current = 'FINISH' 表示所有状态完成", "label": ""}, {"key": "currentStatus", "type": ["String"], "desc": "用于控制 current 指向的步骤条的状态", "label": ""}, {"key": "layout", "type": ["String"], "desc": "步骤条方向，有两种：横向和纵向", "label": ""}, {"key": "readonly", "type": ["Boolean"], "desc": "只读状态", "label": ""}, {"key": "separator", "type": ["String"], "desc": "步骤条分割符", "label": ""}, {"key": "theme", "type": ["String"], "desc": "步骤条风格", "label": ""}], "externalClasses": ["t-class"], "events": [{"key": "bind:change", "desc": "当前步骤发生变化时触发", "label": ""}], "tpl": "<t-steps><t-step-item title=\"步骤描述1\" /><t-step-item title=\"步骤描述2\" /></t-steps>", "require": {"t-step-item": "./step-item/step-item"}, "path": "./steps/steps"}, "t-sticky": {"key": "t-sticky", "label": "吸顶容器", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-sticky.png", "properties": [{"key": "container", "type": ["String"], "desc": "函数返回容器对应的 NodesRef 节点，将对应节点指定为组件的外部容器，滚动时组件会始终保持在容器范围内，当组件即将超出容器底部时，会返回原位置。", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "是否禁用组件", "label": ""}, {"key": "offsetTop", "type": ["String", "Number"], "desc": "吸顶时与顶部的距离，单位`px`", "label": ""}, {"key": "zIndex", "type": ["Number"], "desc": "吸顶时的 z-index", "label": ""}], "externalClasses": ["t-class"], "events": [{"key": "bind:scroll", "desc": "滚动时触发，scrollTop: 距离顶部位置，isFixed: 是否吸顶", "label": ""}], "tpl": "<t-sticky></t-sticky>", "path": "./sticky/sticky"}, "t-swipe-cell": {"key": "t-swipe-cell", "label": "滑动操作", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-swipecell.png", "properties": [{"key": "disabled", "type": ["Boolean"], "desc": "是否禁用滑动", "label": ""}, {"key": "expanded", "type": ["String"], "desc": "操作项是否呈现为打开态", "label": ""}, {"key": "left", "type": ["Array"], "desc": "左侧滑动操作项。所有行为同 `right`", "label": ""}, {"key": "right", "type": ["Array"], "desc": "右侧滑动操作项。有两种定义方式，一种是使用数组，二种是使用插槽。`right.text` 表示操作文本，`right.className` 表示操作项类名，`right.style` 表示操作项样式，`right.onClick` 表示点击操作项后执行的回调函数。示例：`[{ text: '删除', style: 'background-color: red', onClick: () => {} }]`", "label": ""}], "events": [{"key": "bind:click", "desc": "操作项点击时触发（插槽写法组件不触发，业务侧自定义内容和事件）", "label": ""}], "tpl": "<t-swipe-cell><t-cell title=\"列表-左滑单操作\" note=\"辅助信息\" /><view slot=\"right\">删除</view></t-swipe-cell>", "require": {"t-cell": "./cell/cell"}, "path": "./swipe-cell/swipe-cell"}, "t-swiper": {"key": "t-swiper", "label": "轮播", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-swiper.png", "properties": [{"key": "animation", "type": ["String"], "desc": "轮播切换动画效果类型", "label": ""}, {"key": "autoplay", "type": ["Boolean"], "desc": "是否自动播放", "label": ""}, {"key": "current", "type": ["Number"], "desc": "当前轮播在哪一项（下标）", "label": ""}, {"key": "direction", "type": ["String"], "desc": "轮播滑动方向，包括横向滑动和纵向滑动两个方向", "label": ""}, {"key": "duration", "type": ["Number"], "desc": "滑动动画时长", "label": ""}, {"key": "height", "type": ["Number"], "desc": "当使用垂直方向滚动时的高度", "label": ""}, {"key": "interval", "type": ["Number"], "desc": "轮播间隔时间", "label": ""}, {"key": "loop", "type": ["Boolean"], "desc": "是否循环播放", "label": ""}, {"key": "navigation", "type": ["Object"], "desc": "导航器全部配置", "label": ""}], "events": [{"key": "bind:change", "desc": "轮播切换时触发", "label": ""}], "tpl": "<t-swiper current=\"{{1}}\" autoplay=\"{{true}}\" duration=\"{{500}}\" interval=\"{{5000}}\" navigation><t-swiper-item wx:for=\"{{[{image: 'https://tdesign.gtimg.com/site/swiper/01.png'},{image: 'https://tdesign.gtimg.com/site/swiper/02.png'}]}}\" wx:key=\"index\"><image src=\"{{item.image}}\" style=\"width: 100%; height: 100%\" /></t-swiper-item></t-swiper>", "require": {"t-swiper-item": "./swiper/swiper-item"}, "path": "./swiper/swiper"}, "t-switch": {"key": "t-switch", "label": "开关", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-switch.png", "properties": [{"key": "colors", "type": ["Array"], "desc": "自定义颜色，[打开时的颜色，关闭时的颜色]。组件默认颜色为 ['#0052d9', 'rgba(0, 0, 0, .26']。示例：[blue, gray]", "label": ""}, {"key": "customValue", "type": ["Array"], "desc": "开关内容，[打开时的值，关闭时的值]。默认为 [true, false]。示例：[1, 0]", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "是否禁用组件", "label": ""}, {"key": "label", "type": ["String"], "desc": "开关的标签", "label": ""}, {"key": "loading", "type": ["Boolean"], "desc": "是否处于加载中状态", "label": ""}, {"key": "size", "type": ["String"], "desc": "开关尺寸", "label": ""}, {"key": "value", "type": ["String", "Number", "Boolean"], "desc": "开关值", "label": ""}], "events": [{"key": "bind:change", "desc": "数据发生变化时触发", "label": ""}], "tpl": "<t-switch defaultValue=\"{{true}}\" />", "path": "./switch/switch"}, "t-tab-bar-item": {"key": "t-tab-bar-item", "label": "标签栏选项", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-tabbar.png", "properties": [{"key": "badgeProps", "type": ["Object"], "desc": "图标右上角提示信息", "label": ""}, {"key": "icon", "type": ["String"], "desc": "图标名称", "label": ""}, {"key": "subTabBar", "type": ["Array"], "desc": "二级菜单", "label": ""}, {"key": "value", "type": ["String", "Number"], "desc": "标识符", "label": ""}], "tpl": "<t-tab-bar-item wx:for=\"{{[{value: 'label_1',label: '标签栏一',icon: 'app'},{value: 'label_2',label: '标签栏二',icon: 'app'}]}}\" wx:for-item=\"item\" wx:for-index=\"index\" wx:key=\"index\" value=\"{{item.value}}\">{{item.label}}</t-tab-bar-item>", "path": "./tab-bar-item/tab-bar-item"}, "t-tab-bar": {"key": "t-tab-bar", "label": "标签栏", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-tabbar.png", "properties": [{"key": "bordered", "type": ["Boolean"], "desc": "是否显示外边框", "label": ""}, {"key": "color", "type": ["Array"], "desc": "标签颜色设置。示例：[选中标签的颜色, 未选中的标签颜色]", "label": ""}, {"key": "fixed", "type": ["Boolean"], "desc": "是否固定在底部", "label": ""}, {"key": "safeAreaInsetBottom", "type": ["Boolean"], "desc": "是否为 iPhoneX 留出底部安全距离", "label": ""}, {"key": "split", "type": ["Boolean"], "desc": "是否需要分割线", "label": ""}, {"key": "value", "type": ["String", "Number", "Array"], "desc": "当前选中标签的索引", "label": ""}], "externalClasses": ["t-class"], "events": [{"key": "bind:change", "desc": "选中标签切换时触发", "label": ""}], "tpl": "<t-tab-bar value=\"label_1\"><t-tab-bar-item wx:for=\"{{[{value: 'label_1',label: '标签栏一',icon: 'app'},{value: 'label_2',label: '标签栏二',icon: 'app'}]}}\" wx:for-item=\"item\" wx:for-index=\"index\" wx:key=\"index\" value=\"{{item.value}}\">{{item.label}}</t-tab-bar-item></t-tab-bar>", "require": {"t-tab-bar-item": "./tab-bar-item/tab-bar-item"}, "path": "./tab-bar/tab-bar"}, "t-tab-panel": {"key": "t-tab-panel", "label": "选项卡面板", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-tabs.png", "properties": [{"key": "destroyOnHide", "type": ["Boolean"], "desc": "选项卡内容隐藏时是否销毁", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "是否禁用当前选项卡", "label": ""}, {"key": "label", "type": ["String"], "desc": "选项卡名称", "label": ""}, {"key": "panel", "type": ["String"], "desc": "用于自定义选项卡面板内容", "label": ""}, {"key": "value", "type": ["String", "Number"], "desc": "选项卡的值，唯一标识", "label": ""}], "tpl": "<t-tab-panel label=\"标签页一\" value=\"0\">标签一内容</t-tab-panel>", "path": "./tab-panel/tab-panel"}, "t-tabs": {"key": "t-tabs", "label": "选项卡", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-tabs.png", "properties": [{"key": "animation", "type": ["Object"], "desc": "动画效果设置。其中 duration 表示动画时长", "label": ""}, {"key": "placement", "type": ["String"], "desc": "选项卡位置", "label": ""}, {"key": "showBottomLine", "type": ["Boolean"], "desc": "是否展示底部激活线条", "label": ""}, {"key": "stickyProps", "type": ["Object"], "desc": "是否支持吸顶", "label": ""}, {"key": "value", "type": ["String", "Number"], "desc": "激活的选项卡值", "label": ""}], "externalClasses": ["t-class", "t-class-item", "t-class-active", "t-class-track"], "events": [{"key": "bind:change", "desc": "激活的选项卡发生变化时触发", "label": ""}], "tpl": "<t-tabs defaultValue=\"{{0}}\"><t-tab-panel label=\"标签页一\" value=\"0\">标签一内容</t-tab-panel><t-tab-panel label=\"标签页二\" value=\"1\">标签二内容</t-tab-panel></t-tabs>", "require": {"t-tab-panel": "./tab-panel/tab-panel"}, "path": "./tabs/tabs"}, "t-tag": {"key": "t-tag", "label": "标签", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-tag.png", "properties": [{"key": "closable", "type": ["Boolean"], "desc": "标签是否可关闭", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "标签禁用态，失效标签不能触发事件。默认风格（theme=default）才有禁用态", "label": ""}, {"key": "icon", "type": ["String"], "desc": "标签中的图标，可自定义图标呈现", "label": ""}, {"key": "max<PERSON><PERSON><PERSON>", "type": ["String", "Number"], "desc": "标签最大宽度，宽度超出后会出现省略号。示例：'50px' / 80", "label": ""}, {"key": "shape", "type": ["String"], "desc": "标签类型，有三种：方形、圆角方形、标记型", "label": ""}, {"key": "size", "type": ["String"], "desc": "标签尺寸", "label": ""}, {"key": "theme", "type": ["String"], "desc": "组件风格，用于描述组件不同的应用场景", "label": ""}, {"key": "variant", "type": ["String"], "desc": "标签风格变体", "label": ""}], "externalClasses": ["t-class"], "events": [{"key": "bind:click", "desc": "点击时触发", "label": ""}, {"key": "bind:close", "desc": "如果关闭按钮存在，点击关闭按钮时触发", "label": ""}], "tpl": "<t-tag theme=\"primary\">重要</t-tag>", "path": "./tag/tag"}, "t-textarea": {"key": "t-textarea", "label": "文本输入框", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-textarea.png", "properties": [{"key": "adjustPosition", "type": ["Boolean"], "desc": "键盘弹起时，是否自动上推页面", "label": ""}, {"key": "autofocus", "type": ["Boolean"], "desc": "自动聚焦，拉起键盘", "label": ""}, {"key": "autosize", "type": ["Boolean"], "desc": "是否自动增高，值为 autosize 时，style.height 不生效", "label": ""}, {"key": "confirmHold", "type": ["Boolean"], "desc": "点击键盘右下角按钮时是否保持键盘不收起点", "label": ""}, {"key": "confirmType", "type": ["String"], "desc": "设置键盘右下角按钮的文字，仅在 type='text'时生效", "label": ""}, {"key": "disabled", "type": ["Boolean"], "desc": "是否禁用文本框", "label": ""}, {"key": "focus", "type": ["Boolean"], "desc": "自动聚焦", "label": ""}, {"key": "label", "type": ["String"], "desc": "左侧文本", "label": ""}, {"key": "max<PERSON><PERSON>ter", "type": ["Number"], "desc": "用户最多可以输入的字符个数，一个中文汉字表示两个字符长度", "label": ""}, {"key": "maxlength", "type": ["Number"], "desc": "用户最多可以输入的字符个数", "label": ""}, {"key": "placeholder", "type": ["String"], "desc": "占位符", "label": ""}, {"key": "value", "type": ["String"], "desc": "文本框值", "label": ""}], "externalClasses": ["t-class", "t-class-textarea", "t-class-placeholder", "t-class-name"], "events": [{"key": "bind:blur", "desc": "失去焦点时触发", "label": ""}, {"key": "bind:change", "desc": "输入内容变化时触发", "label": ""}, {"key": "bind:enter", "desc": "点击完成时触发", "label": ""}, {"key": "bind:focus", "desc": "获得焦点时触发", "label": ""}, {"key": "bind:line-change", "desc": "行高发生变化时触发", "label": ""}], "tpl": "<t-textarea />", "path": "./textarea/textarea"}, "t-toast": {"key": "t-toast", "label": "轻提示", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-toast.png", "properties": [{"key": "direction", "type": ["String"], "desc": "图标排列方式", "label": ""}, {"key": "duration", "type": ["Number"], "desc": "弹窗显示毫秒数", "label": ""}, {"key": "icon", "type": ["String"], "desc": "自定义图标", "label": ""}, {"key": "message", "type": ["String"], "desc": "弹窗显示文字", "label": ""}, {"key": "overlayProps", "type": ["Object"], "desc": "遮罩层属性，透传至 Overlay", "label": ""}, {"key": "placement", "type": ["String"], "desc": "弹窗展示位置", "label": ""}, {"key": "preventScrollThrough", "type": ["Boolean"], "desc": "防止滚动穿透，即不允许点击和滚动", "label": ""}, {"key": "showOverlay", "type": ["Boolean"], "desc": "是否显示遮罩层", "label": ""}, {"key": "theme", "type": ["String"], "desc": "提示类型", "label": ""}], "externalClasses": ["t-class"], "tpl": "<t-toast id=\"t-toast\" />", "path": "./toast/toast"}, "t-transition": {"key": "t-transition", "label": "动画", "icon": "", "properties": [{"key": "appear", "type": ["Boolean"], "desc": "首次出现是否展示动画", "label": ""}, {"key": "customClass", "type": ["String"], "desc": "自定义容器类名", "label": ""}, {"key": "destoryOnClose", "type": ["Boolean"], "desc": "隐藏时是否销毁内容", "label": ""}, {"key": "duration", "type": ["Number"], "desc": "指定过渡时间", "label": ""}, {"key": "name", "type": ["String"], "desc": "过渡类名", "label": ""}, {"key": "visible", "type": ["Boolean"], "desc": "是否显示", "label": ""}], "tpl": "<t-transition visible appear></t-transition>", "path": "./transition/transition"}, "t-upload": {"key": "t-upload", "label": "上传", "icon": "https://tdesign.gtimg.com/site/miniprogram-doc/doc-upload.png", "properties": [{"key": "addContent", "type": ["String"], "desc": "添加按钮内容。值为空，使用默认图标渲染；值为 slot 则表示使用插槽渲染；其他值无效。", "label": ""}, {"key": "allowUploadDuplicateFile", "type": ["Boolean"], "desc": "是否允许重复上传相同文件名的文件", "label": ""}, {"key": "config", "type": ["Object"], "desc": "图片上传配置，视频上传配置，文件上传配置等，包含图片尺寸、图片来源、视频来源、视频拍摄最长时间等。更多细节查看小程序官网。[图片上传](https://developers.weixin.qq.com/miniprogram/dev/api/media/image/wx.chooseImage.html)。[视频上传](https://developers.weixin.qq.com/miniprogram/dev/api/media/video/wx.chooseVideo.html)", "label": ""}, {"key": "deleteBtn", "type": ["String"], "desc": "删除图标。值为空，使用默认图标渲染；值为 slot 则表示使用插槽渲染；其他值无效。", "label": ""}, {"key": "fileListDisplay", "type": ["String"], "desc": "用于完全自定义文件列表内容", "label": ""}, {"key": "files", "type": ["Array"], "desc": "已上传文件列表", "label": ""}, {"key": "gridConfig", "type": ["Object"], "desc": "upload组件每行上传图片列数以及图片的宽度和高度", "label": ""}, {"key": "gutter", "type": ["Number"], "desc": "预览窗格的 `gutter` 大小，单位 rpx", "label": ""}, {"key": "imageProps", "type": ["Object"], "desc": "透传 Image 组件全部属性", "label": ""}, {"key": "max", "type": ["Number"], "desc": "用于控制文件上传数量，值为 0 则不限制", "label": ""}, {"key": "mediaType", "type": ["Array"], "desc": "支持上传的文件类型，图片或视频", "label": ""}, {"key": "requestMethod", "type": ["String"], "desc": "自定义上传方法", "label": ""}, {"key": "sizeLimit", "type": ["Number", "Object"], "desc": "图片文件大小限制，单位 KB。可选单位有：`'B' | 'KB' | 'MB' | 'GB'`。示例一：`1000`。示例二：`{ size: 2, unit: 'MB', message: '图片大小不超过 {sizeLimit} MB' }`", "label": ""}], "events": [{"key": "bind:add", "desc": "上传成功后触发，仅包含本次选择的照片；`url` 表示选定视频的临时文件路径 (本地路径)。`duration` 表示选定视频的时间长度。`size`选定视频的数据量大小。更多描述参考 wx.chooseMedia 小程序官网描述", "label": ""}, {"key": "bind:complete", "desc": "上传成功或失败后触发", "label": ""}, {"key": "bind:fail", "desc": "上传失败后触发", "label": ""}, {"key": "bind:remove", "desc": "移除文件时触发", "label": ""}, {"key": "bind:select-change", "desc": "选择文件或图片之后，上传之前，触发该事件。<br />`files` 表示之前已经上传完成的文件列表。<br />`currentSelectedFiles` 表示本次上传选中的文件列表", "label": ""}, {"key": "bind:success", "desc": "上传成功后触发，包含所有上传的文件；`url` 表示选定视频的临时文件路径 (本地路径)。`duration` 表示选定视频的时间长度。`size`选定视频的数据量大小。更多描述参考 wx.chooseMedia 小程序官网描述", "label": ""}], "tpl": "<t-upload />", "path": "./upload/upload"}}, "common": {"properties": {}, "events": {}}, "menu": [{"key": "menu-basic", "label": "基础", "submenu": [{"key": "subMenu-button", "label": "Button 按钮", "components": ["t-button"]}, {"key": "subMenu-divider", "label": "Divider 分割线", "components": ["t-divider"]}, {"key": "subMenu-fab", "label": "Fab 悬浮按钮", "components": ["t-fab"]}, {"key": "subMenu-icon", "label": "Icon 图标", "components": ["t-icon"]}]}, {"key": "menu-nav", "label": "导航", "submenu": [{"key": "subMenu-drawer", "label": "Drawer 抽屉", "components": ["t-drawer"]}, {"key": "subMenu-indexes", "label": "Indexes 索引", "components": ["t-indexes"]}, {"key": "subMenu-navbar", "label": "Navbar 导航条", "components": ["t-navbar"]}, {"key": "subMenu-steps", "label": "Steps 步骤条", "components": ["t-steps"]}, {"key": "subMenu-tabbar", "label": "TabBar 标签栏", "components": ["t-tab-bar"]}, {"key": "subMenu-tabs", "label": "Tabs 选项卡", "components": ["t-tabs"]}]}, {"key": "menu-input", "label": "输入", "submenu": [{"key": "subMenu-checkbox", "label": "CheckBox 复选框", "components": ["t-checkbox"]}, {"key": "subMenu-dateTimePicker", "label": "DateTimePicker 时间选择器", "components": ["t-date-time-picker"]}, {"key": "subMenu-input", "label": "Input 输入框", "components": ["t-input"]}, {"key": "subMenu-picker", "label": "Picker 选择器", "components": ["t-picker"]}, {"key": "subMenu-radio", "label": "Radio 单选框", "components": ["t-radio"]}, {"key": "subMenu-rate", "label": "Rate 评分", "components": ["t-rate"]}, {"key": "subMenu-search", "label": "Search 搜索框", "components": ["t-search"]}, {"key": "subMenu-slider", "label": "Slider 滑动选择器", "components": ["t-slider"]}, {"key": "subMenu-stepper", "label": "Stepper 步进器", "components": ["t-stepper"]}, {"key": "subMenu-switch", "label": "Switch 开关", "components": ["t-switch"]}, {"key": "subMenu-textarea", "label": "Textarea 多行文本框", "components": ["t-textarea"]}, {"key": "subMenu-upload", "label": "UpLoad 上传", "components": ["t-upload"]}]}, {"key": "menu-data", "label": "数据展示", "submenu": [{"key": "subMenu-avatar", "label": "Avatar 头像", "components": ["t-avatar"]}, {"key": "subMenu-badge", "label": "Badge 徽标", "components": ["t-badge"]}, {"key": "subMenu-cell", "label": "Cell 单元格", "components": ["t-cell"]}, {"key": "subMenu-collapse", "label": "Collapse 折叠面板", "components": ["t-collapse"]}, {"key": "subMenu-dropdown-menu", "label": "DropdownMenu 下拉菜单", "components": ["t-dropdown-menu"]}, {"key": "subMenu-empty", "label": "Empty 空状态", "components": ["t-empty"]}, {"key": "subMenu-footer", "label": "Footer 页脚", "components": ["t-footer"]}, {"key": "subMenu-grid", "label": "Grid 宫格", "components": ["t-grid"]}, {"key": "subMenu-image", "label": "Image 图片", "components": ["t-image"]}, {"key": "subMenu-skeleton", "label": "Skeleton 骨架屏", "components": ["t-skeleton"]}, {"key": "subMenu-sticky", "label": "Sticky 吸顶容器", "components": ["t-sticky"]}, {"key": "subMenu-swiper", "label": "Swiper 轮播图", "components": ["t-swiper"]}, {"key": "subMenu-tag", "label": "Tag 标签", "components": ["t-tag"]}]}, {"key": "menu-info", "label": "消息提醒", "submenu": [{"key": "subMenu-actionsheet", "label": "ActionSheet 动作面板", "components": ["t-action-sheet"]}, {"key": "subMenu-back-top", "label": "BackTop 返回顶部", "components": ["t-back-top"]}, {"key": "subMenu-dialog", "label": "Dialog 弹出框", "components": ["t-dialog"]}, {"key": "subMenu-loading", "label": "Loading 加载", "components": ["t-loading"]}, {"key": "subMenu-message", "label": "Message 消息通知", "components": ["t-message"]}, {"key": "subMenu-popup", "label": "Popup 弹出层", "components": ["t-popup"]}, {"key": "subMenu-progress", "label": "Progress 进度条", "components": ["t-progress"]}, {"key": "subMenu-pullDownRefresh", "label": "PullDownRefresh 下拉刷新", "components": ["t-pull-down-refresh"]}, {"key": "subMenu-swipeCell", "label": "SwipeCell 滑动操作", "components": ["t-swipe-cell"]}, {"key": "subMenu-toast", "label": "Toast 轻提示", "components": ["t-toast"]}]}]}