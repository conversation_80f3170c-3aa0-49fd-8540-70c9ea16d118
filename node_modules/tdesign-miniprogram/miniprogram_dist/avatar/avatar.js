import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import avatarProps from"./props";import{setIcon,systemInfo}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-avatar`;let Avatar=class extends SuperComponent{constructor(){super(...arguments),this.options={multipleSlots:!0},this.externalClasses=[`${prefix}-class`,`${prefix}-class-image`,`${prefix}-class-icon`,`${prefix}-class-alt`,`${prefix}-class-content`],this.properties=avatarProps,this.data={prefix:prefix,classPrefix:name,isShow:!0,zIndex:0,systemInfo:systemInfo},this.relations={"../avatar-group/avatar-group":{type:"ancestor",linked(t){this.parent=t,this.setData({shape:this.data.shape||t.data.shape||"circle",size:this.data.size||t.data.size,bordered:!0})}}},this.observers={icon(t){const s=setIcon("icon",t,"");this.setData(Object.assign({},s))}},this.methods={hide(){this.setData({isShow:!1})},onLoadError(t){this.properties.hideOnLoadFailed&&this.setData({isShow:!1}),this.triggerEvent("error",t.detail)}}}};Avatar=__decorate([wxComponent()],Avatar);export default Avatar;