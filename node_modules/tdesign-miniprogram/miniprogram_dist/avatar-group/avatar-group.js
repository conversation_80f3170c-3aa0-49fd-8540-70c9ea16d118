import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import avatarGroupProps from"./props";const{prefix:prefix}=config,name=`${prefix}-avatar-group`;let AvatarGroup=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-content`,`${prefix}-class-image`],this.properties=avatarGroupProps,this.data={prefix:prefix,classPrefix:name,hasChild:!0,length:0,className:""},this.options={multipleSlots:!0},this.relations={"../avatar/avatar":{type:"descendant"}},this.lifetimes={attached(){this.setClass()},ready(){this.setData({length:this.$children.length}),this.handleMax()}},this.observers={"cascading, size"(){this.setClass()}},this.methods={setClass(){const{cascading:e,size:t}=this.properties,s=e.split("-")[0],a=[name,`${prefix}-class`,`${name}-offset-${s}`,`${name}-offset-${s}-${t.indexOf("px")>-1?"medium":t||"medium"}`];this.setData({className:a.join(" ")})},handleMax(){const{max:e}=this.data,t=this.$children.length;if(!e||e>t)return;this.$children.splice(e,t-e).forEach(e=>{e.hide()})},onCollapsedItemClick(e){this.triggerEvent("collapsed-item-click",e.detail)}}}};AvatarGroup=__decorate([wxComponent()],AvatarGroup);export default AvatarGroup;