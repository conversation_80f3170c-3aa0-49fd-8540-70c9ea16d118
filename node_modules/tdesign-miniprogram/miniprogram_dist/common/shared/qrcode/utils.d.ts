import type { CrossOrigin, ERROR_LEVEL_MAPPED_TYPE, ErrorCorrectionLevel, Excavation, ImageSettings } from './types';
export declare const ERROR_LEVEL_MAP: ERROR_LEVEL_MAPPED_TYPE;
export declare const DEFAULT_SIZE = 160;
export declare const DEFAULT_LEVEL: ErrorCorrectionLevel;
export declare const DEFAULT_BACKGROUND_COLOR = "#FFFFFF";
export declare const DEFAULT_FRONT_COLOR = "#000000";
export declare const DEFAULT_NEED_MARGIN = false;
export declare const DEFAULT_MINVERSION = 1;
export declare const SPEC_MARGIN_SIZE = 4;
export declare const DEFAULT_MARGIN_SIZE = 0;
export declare const DEFAULT_IMG_SCALE = 0.1;
export declare const generatePath: (modules: boolean[][], margin?: number) => string;
export declare const excavateModules: (modules: boolean[][], excavation: Excavation) => boolean[][];
export declare const getImageSettings: (cells: boolean[][], size: number, margin: number, imageSettings?: ImageSettings) => {
    x: number;
    y: number;
    h: number;
    w: number;
    excavation: Excavation | null;
    opacity: number;
    crossOrigin: CrossOrigin;
};
export declare const getMarginSize: (needMargin: boolean, marginSize?: number) => number;
export declare const isSupportPath2d: boolean;
