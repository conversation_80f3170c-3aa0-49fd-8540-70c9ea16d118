import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-collapse`;let Collapse=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`],this.relations={"../collapse-panel/collapse-panel":{type:"descendant"}},this.controlledProps=[{key:"value",event:"change"}],this.properties=props,this.data={prefix:prefix,classPrefix:name},this.observers={"value, expandMutex "(){this.updateExpanded()}},this.methods={updateExpanded(){this.$children.forEach(e=>{e.updateExpanded(this.properties.value)})},switch(e){const{expandMutex:t,value:o}=this.properties;let p=[];p=o.indexOf(e)>-1?o.filter(t=>t!==e):t?[e]:o.concat(e),this._trigger("change",{value:p})}}}};Collapse=__decorate([wxComponent()],Collapse);export default Collapse;