<wxs src="../common/utils.wxs" module="_"/><wxs src="./step-item.wxs" module="t"/><view style="{{_._style([style, customStyle])}}" class="{{_.cls(classPrefix, [layout, ['readonly', readonly]])}} class {{prefix}}-class" bind:tap="onTap" aria-role="{{ariaRole || readonly ? 'option' : 'button'}}" aria-label="{{ariaLabel || t.getAriaLabel(index, title, content)}}" aria-current="{{curStatus == 'process'? 'step': ''}}"><view class="{{_.cls(classPrefix + '__anchor', [layout])}}" aria-hidden="true"><view wx:if="{{isDot}}" class="{{_.cls(classPrefix + '__dot', [curStatus])}}"/><view wx:elif="{{icon}}" class="{{_.cls(classPrefix + '__icon', [curStatus])}}"><slot wx:if="{{icon == 'slot'}}" name="icon"/><t-icon wx:else name="{{icon}}" size="44rpx"/></view><view wx:else class="{{_.cls(classPrefix + '__circle', [curStatus])}}"><t-icon wx:if="{{curStatus == 'finish'}}" name="check"/><t-icon wx:elif="{{curStatus == 'error'}}" name="close"/><block wx:else>{{index + 1}}</block></view></view><view class="{{_.cls(classPrefix + '__content', [layout, ['last', isLastChild]])}} {{prefix}}-class-content" aria-hidden="true"><slot/><view class="{{_.cls(classPrefix + '__title', [curStatus, layout])}} {{prefix}}-class-title"><block wx:if="{{title}}">{{ title }}</block><slot name="title"/><slot wx:if="{{layout === 'vertical'}}" name="title-right"/></view><view class="{{_.cls(classPrefix + '__description', [layout])}} {{prefix}}-class-description"><block wx:if="{{content}}">{{ content }}</block><slot name="content"/></view><view class="{{_.cls(classPrefix + '__extra', [layout])}} {{prefix}}-class-extra"><slot name="extra"/></view></view><view wx:if="{{!isLastChild}}" class="{{_.cls(classPrefix + '__line', [curStatus, layout, theme, sequence])}}" aria-hidden="true"/></view>