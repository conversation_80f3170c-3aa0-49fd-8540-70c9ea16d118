{"name": "miniprogram-api-typings", "version": "4.1.0", "description": "Type definitions for APIs of Wechat Mini Program in TypeScript", "main": "./index.d.ts", "types": "./index.d.ts", "scripts": {"test": "npm run tsd && npm run eslint", "tsd": "tsd", "eslint": "eslint --config .eslintrc.js types/**/*.ts"}, "repository": {"type": "git", "url": "git+https://github.com/wechat-miniprogram/api-typings.git"}, "author": "Wechat Miniprogram <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/wechat-miniprogram/api-typings/issues"}, "homepage": "https://github.com/wechat-miniprogram/api-typings#readme", "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "tsd": "^0.32.0", "typescript": "^5.2.2 <5.4.0"}, "tsd": {"directory": "test"}, "files": ["LICENSE", "CHANGELOG.md", "README.md", "README-en.md", "index.d.ts", "typings.json", "types/"]}