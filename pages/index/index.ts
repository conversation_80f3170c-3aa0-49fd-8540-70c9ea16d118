// index.ts
// 获取应用实例
const app = getApp<IAppOption>()

Component({
  data: {
    // 页面数据
  },

  methods: {
    // 开始AI问诊
    startAIConsultation() {
      wx.navigateTo({
        url: '/pages/ai-consultation/index'
      })
    },

    // 跳转到预约挂号
    goToAppointment() {
      wx.navigateTo({
        url: '/pages/appointment/index'
      })
    },

    // 跳转到我的预约
    goToMyAppointments() {
      wx.switchTab({
        url: '/pages/my-appointments/index'
      })
    },

    // 显示即将上线提示
    showComingSoon() {
      wx.showToast({
        title: '功能即将上线',
        icon: 'none',
        duration: 2000
      })
    }
  },

  // 页面生命周期
  lifetimes: {
    attached() {
      // 页面加载时的逻辑
      console.log('首页加载完成')
    }
  }
})
