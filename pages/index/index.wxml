<!--index.wxml-->
<view class="page-container">
  <!-- 顶部品牌区域 -->
  <view class="header-section">
    <view class="hospital-info">
      <view class="hospital-logo">
        <image src="https://img.icons8.com/color/96/hospital.png" class="logo-image"></image>
      </view>
      <view class="hospital-text">
        <text class="hospital-name">西虹市第一人民医院</text>
        <text class="hospital-subtitle">AI智能预约助手</text>
      </view>
    </view>
    <view class="ai-doctor-avatar">
      <image src="https://img.icons8.com/color/96/doctor-female.png" class="doctor-image"></image>
    </view>
  </view>

  <!-- AI问诊核心功能区域 -->
  <view class="ai-section">
    <t-card class="ai-card">
      <view class="ai-content">
        <view class="ai-icon">🤖</view>
        <view class="ai-text">
          <text class="ai-title">AI智能问诊</text>
          <text class="ai-desc">告诉我哪里不舒服，智能推荐科室医生</text>
        </view>
      </view>
      <t-button
        class="ai-button"
        type="primary"
        size="large"
        bind:tap="startAIConsultation">
        开始对话
      </t-button>
    </t-card>
  </view>

  <!-- 传统功能区域 -->
  <view class="traditional-section">
    <t-grid column="2" class="function-grid">
      <t-grid-item
        text="预约挂号"
        icon="https://img.icons8.com/color/48/appointment-scheduling.png"
        bind:click="goToAppointment">
      </t-grid-item>
      <t-grid-item
        text="我的预约"
        icon="https://img.icons8.com/color/48/calendar.png"
        bind:click="goToMyAppointments">
      </t-grid-item>
    </t-grid>
  </view>

  <!-- 服务网格区域 -->
  <view class="services-section">
    <view class="section-title">相关服务</view>
    <t-grid column="4" class="services-grid">
      <t-grid-item
        text="我的预约"
        icon="https://img.icons8.com/color/48/calendar-plus.png"
        bind:click="goToMyAppointments">
      </t-grid-item>
      <t-grid-item
        text="门诊费用"
        icon="https://img.icons8.com/color/48/money.png"
        bind:click="showComingSoon">
      </t-grid-item>
      <t-grid-item
        text="门诊报告"
        icon="https://img.icons8.com/color/48/medical-report.png"
        bind:click="showComingSoon">
      </t-grid-item>
      <t-grid-item
        text="影像报告"
        icon="https://img.icons8.com/color/48/x-ray.png"
        bind:click="showComingSoon">
      </t-grid-item>
      <t-grid-item
        text="电子票据"
        icon="https://img.icons8.com/color/48/receipt.png"
        bind:click="showComingSoon">
      </t-grid-item>
      <t-grid-item
        text="门诊历史"
        icon="https://img.icons8.com/color/48/medical-history.png"
        bind:click="showComingSoon">
      </t-grid-item>
      <t-grid-item
        text="门诊结算"
        icon="https://img.icons8.com/color/48/cash-register.png"
        bind:click="showComingSoon">
      </t-grid-item>
      <t-grid-item
        text="线上退款"
        icon="https://img.icons8.com/color/48/refund.png"
        bind:click="showComingSoon">
      </t-grid-item>
    </t-grid>
  </view>

  <!-- 健康讲堂 -->
  <view class="health-section">
    <view class="section-title">健康讲堂</view>
    <t-card class="health-card">
      <view class="health-content">
        <text class="health-title">冬季感冒预防小贴士</text>
        <text class="health-desc">了解如何在寒冷季节保护自己...</text>
      </view>
    </t-card>
  </view>
</view>

<!-- Toast组件 -->
<t-toast id="t-toast" />
