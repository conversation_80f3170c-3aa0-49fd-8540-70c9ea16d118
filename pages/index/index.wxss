/**index.wxss**/

/* 页面容器 */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部品牌区域 */
.header-section {
  background: linear-gradient(135deg, #0052D9 0%, #0085FF 100%);
  padding: 40rpx 32rpx 60rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  position: relative;
}

.hospital-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.hospital-logo {
  margin-right: 24rpx;
}

.logo-image {
  width: 80rpx;
  height: 80rpx;
}

.hospital-text {
  display: flex;
  flex-direction: column;
}

.hospital-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.hospital-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

.ai-doctor-avatar {
  position: relative;
}

.doctor-image {
  width: 120rpx;
  height: 120rpx;
}

/* AI问诊核心功能区域 */
.ai-section {
  padding: 32rpx;
  margin-top: -30rpx;
  position: relative;
  z-index: 2;
}

.ai-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 82, 217, 0.15);
}

.ai-content {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.ai-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.ai-text {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.ai-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #000000;
  margin-bottom: 8rpx;
}

.ai-desc {
  font-size: 28rpx;
  color: #666666;
}

.ai-button {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background: linear-gradient(135deg, #0052D9 0%, #0085FF 100%);
  font-size: 32rpx;
  font-weight: 500;
}

/* 传统功能区域 */
.traditional-section {
  padding: 0 32rpx 32rpx;
}

.function-grid {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

/* 服务网格区域 */
.services-section {
  padding: 0 32rpx 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 24rpx;
}

.services-grid {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

/* 健康讲堂 */
.health-section {
  padding: 0 32rpx 32rpx;
}

.health-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
}

.health-content {
  display: flex;
  flex-direction: column;
}

.health-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 16rpx;
}

.health-desc {
  font-size: 28rpx;
  color: #666666;
}
