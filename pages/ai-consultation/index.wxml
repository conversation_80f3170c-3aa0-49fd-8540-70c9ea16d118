<!--ai-consultation/index.wxml-->
<view class="chat-container">
  <!-- 聊天消息列表 -->
  <scroll-view 
    class="chat-messages" 
    scroll-y 
    scroll-top="{{scrollTop}}"
    scroll-into-view="{{scrollIntoView}}">
    
    <view wx:for="{{messages}}" wx:key="id" class="message-item" id="msg-{{item.id}}">
      <!-- AI消息 -->
      <view wx:if="{{item.type === 'ai'}}" class="message ai-message">
        <view class="avatar-container">
          <image src="https://img.icons8.com/color/48/chatbot.png" class="avatar ai-avatar"></image>
        </view>
        <view class="message-content ai-content">
          <text class="message-text">{{item.content}}</text>
          <!-- AI选项按钮 -->
          <view wx:if="{{item.options && item.options.length > 0}}" class="options-container">
            <view 
              wx:for="{{item.options}}" 
              wx:for-item="option" 
              wx:key="*this"
              class="option-button"
              bind:tap="selectOption"
              data-option="{{option}}">
              {{option}}
            </view>
          </view>
        </view>
      </view>

      <!-- 用户消息 -->
      <view wx:else class="message user-message">
        <view class="message-content user-content">
          <text class="message-text">{{item.content}}</text>
        </view>
        <view class="avatar-container">
          <image src="https://img.icons8.com/color/48/user.png" class="avatar user-avatar"></image>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{isLoading}}" class="message ai-message">
      <view class="avatar-container">
        <image src="https://img.icons8.com/color/48/chatbot.png" class="avatar ai-avatar"></image>
      </view>
      <view class="message-content ai-content">
        <view class="typing-indicator">
          <text>AI正在思考中</text>
          <view class="dots">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="input-area">
    <view class="input-container">
      <t-input
        class="message-input"
        placeholder="请描述您的症状..."
        value="{{inputValue}}"
        bind:change="onInputChange"
        bind:confirm="sendMessage"
        confirm-type="send"
        maxlength="200"
      />
      <t-button 
        class="send-button" 
        type="primary" 
        size="small"
        bind:tap="sendMessage"
        disabled="{{!inputValue.trim()}}">
        发送
      </t-button>
    </view>
  </view>
</view>

<!-- Toast组件 -->
<t-toast id="t-toast" />
