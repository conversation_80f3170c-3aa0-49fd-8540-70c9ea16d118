// ai-consultation/index.ts
import { aiConsultationService } from '../../services/ai-consultation';
import { ChatMessage } from '../../data/mock/index';

Component({
  data: {
    messages: [] as ChatMessage[],
    inputValue: '',
    isLoading: false,
    scrollTop: 0,
    scrollIntoView: ''
  },

  lifetimes: {
    attached() {
      // 初始化AI问诊
      this.initConsultation();
    }
  },

  methods: {
    // 初始化问诊
    initConsultation() {
      const welcomeMessage = aiConsultationService.initConsultation();
      this.setData({
        messages: [welcomeMessage]
      });
      this.scrollToBottom();
    },

    // 发送消息
    async sendMessage() {
      const { inputValue } = this.data;
      if (!inputValue.trim()) return;

      // 显示加载状态
      this.setData({
        isLoading: true,
        inputValue: ''
      });

      try {
        // 处理用户输入
        const newMessages = await aiConsultationService.processUserInput(inputValue);

        // 更新消息列表
        this.setData({
          messages: [...this.data.messages, ...newMessages],
          isLoading: false
        });

        this.scrollToBottom();
      } catch (error) {
        console.error('发送消息失败:', error);
        this.setData({
          isLoading: false
        });

        wx.showToast({
          title: '发送失败，请重试',
          icon: 'none'
        });
      }
    },

    // 选择选项
    async selectOption(e: any) {
      const option = e.currentTarget.dataset.option;
      if (!option) return;

      // 处理特殊选项
      if (option === '查看预约详情') {
        wx.switchTab({
          url: '/pages/my-appointments/index'
        });
        return;
      }

      if (option === '返回首页') {
        wx.switchTab({
          url: '/pages/index/index'
        });
        return;
      }

      if (option === '继续咨询') {
        this.restart();
        return;
      }

      // 显示加载状态
      this.setData({
        isLoading: true
      });

      try {
        // 处理选项选择
        const newMessages = await aiConsultationService.processUserInput(option);

        // 更新消息列表
        this.setData({
          messages: [...this.data.messages, ...newMessages],
          isLoading: false
        });

        this.scrollToBottom();
      } catch (error) {
        console.error('选择选项失败:', error);
        this.setData({
          isLoading: false
        });

        wx.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      }
    },

    // 输入变化
    onInputChange(e: any) {
      this.setData({
        inputValue: e.detail.value
      });
    },

    // 滚动到底部
    scrollToBottom() {
      const { messages } = this.data;
      if (messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        this.setData({
          scrollIntoView: `msg-${lastMessage.id}`
        });
      }
    },

    // 重新开始
    restart() {
      aiConsultationService.reset();
      this.initConsultation();
    }
  }
});
