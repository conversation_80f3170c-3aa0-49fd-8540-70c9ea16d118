/**ai-consultation/index.wxss**/

/* 聊天容器 */
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 消息列表 */
.chat-messages {
  flex: 1;
  padding: 32rpx 24rpx;
  overflow-y: auto;
}

/* 消息项 */
.message-item {
  margin-bottom: 32rpx;
}

.message {
  display: flex;
  align-items: flex-start;
  max-width: 100%;
}

/* AI消息样式 */
.ai-message {
  justify-content: flex-start;
}

.ai-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.ai-content {
  background-color: #ffffff;
  border-radius: 0 24rpx 24rpx 24rpx;
  padding: 24rpx;
  max-width: 70%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 用户消息样式 */
.user-message {
  justify-content: flex-end;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-left: 16rpx;
}

.user-content {
  background: linear-gradient(135deg, #0052D9 0%, #0085FF 100%);
  color: white;
  border-radius: 24rpx 0 24rpx 24rpx;
  padding: 24rpx;
  max-width: 70%;
}

/* 消息文本 */
.message-text {
  font-size: 32rpx;
  line-height: 1.5;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 选项按钮容器 */
.options-container {
  margin-top: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.option-button {
  background-color: #f0f8ff;
  border: 2rpx solid #0052D9;
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
  text-align: center;
  font-size: 28rpx;
  color: #0052D9;
  transition: all 0.3s ease;
}

.option-button:active {
  background-color: #0052D9;
  color: white;
  transform: scale(0.98);
}

/* 加载状态 */
.typing-indicator {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
}

.dots {
  display: flex;
  margin-left: 16rpx;
}

.dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #0052D9;
  margin: 0 2rpx;
  animation: typing 1.4s infinite ease-in-out;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 */
.input-area {
  background-color: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  padding: 24rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
}

.input-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.message-input {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
  font-size: 32rpx;
}

.send-button {
  width: 120rpx;
  height: 64rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
}

/* 头像容器 */
.avatar-container {
  flex-shrink: 0;
}

.avatar {
  display: block;
}
