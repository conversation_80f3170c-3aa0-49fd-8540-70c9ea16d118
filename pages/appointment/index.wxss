/**appointment/index.wxss**/

/* 页面容器 */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 32rpx;
}

/* 科室选择区域 */
.department-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #000000;
  margin-bottom: 24rpx;
}

.department-grid {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

/* 医生列表区域 */
.doctors-section {
  margin-bottom: 32rpx;
}

.doctors-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 医生卡片 */
.doctor-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
}

.doctor-info {
  display: flex;
  margin-bottom: 24rpx;
}

.doctor-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.doctor-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.doctor-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.doctor-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #000000;
  margin-right: 16rpx;
}

.doctor-title {
  font-size: 28rpx;
  color: #0052D9;
  background-color: #f0f8ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.doctor-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.doctor-experience {
  font-size: 28rpx;
  color: #666666;
}

.doctor-rating {
  display: flex;
  align-items: center;
}

.rating-text {
  font-size: 28rpx;
  color: #FF9500;
}

.doctor-specialty {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.specialty-label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.specialty-text {
  font-size: 28rpx;
  color: #000000;
  flex: 1;
}

.doctor-fee {
  display: flex;
  align-items: center;
}

.fee-label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 8rpx;
}

.fee-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #E34D59;
}

/* 可预约时间 */
.available-slots {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 24rpx;
}

.slots-title {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 16rpx;
  display: block;
}

.slots-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.slot-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.slot-item.available {
  background-color: #f0f8ff;
  border: 1rpx solid #0052D9;
}

.slot-item.unavailable {
  background-color: #f5f5f5;
  color: #999999;
}

.slot-date {
  font-weight: 500;
}

.slot-time {
  color: #666666;
}

.slot-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.available .slot-status {
  background-color: #00A870;
  color: white;
}

.unavailable .slot-status {
  background-color: #BBBBBB;
  color: white;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666666;
}
