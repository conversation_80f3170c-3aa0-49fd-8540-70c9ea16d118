// appointment/index.ts
import { departments, doctors, Doctor, Department } from '../../data/mock/index';
import { reminderService } from '../../services/reminder';

Component({
  data: {
    departments: [] as Department[],
    doctors: [] as Doctor[],
    selectedDepartment: null as Department | null,
    filteredDoctors: [] as Doctor[]
  },

  lifetimes: {
    attached() {
      this.loadData();
    }
  },

  methods: {
    // 加载数据
    loadData() {
      this.setData({
        departments,
        doctors
      });
    },

    // 选择科室
    selectDepartment(e: any) {
      const department = e.currentTarget.dataset.department;

      // 筛选该科室的医生
      const filteredDoctors = doctors.filter(doctor =>
        doctor.department === department.name
      );

      this.setData({
        selectedDepartment: department,
        filteredDoctors
      });
    },

    // 选择医生
    selectDoctor(e: any) {
      const doctor = e.currentTarget.dataset.doctor;

      // 检查是否有可预约时间
      const availableSlots = doctor.availableSlots.filter((slot: any) => slot.available);

      if (availableSlots.length === 0) {
        wx.showToast({
          title: '该医生暂无可预约时间',
          icon: 'none'
        });
        return;
      }

      // 显示预约时间选择
      this.showTimeSelection(doctor, availableSlots);
    },

    // 显示时间选择
    showTimeSelection(doctor: Doctor, availableSlots: any[]) {
      const timeOptions = availableSlots.map(slot => {
        const date = new Date(slot.date);
        const dateStr = `${date.getMonth() + 1}月${date.getDate()}日`;
        const periodStr = slot.period === 'morning' ? '上午' : '下午';
        return `${dateStr}${periodStr} ${slot.time}`;
      });

      wx.showActionSheet({
        itemList: timeOptions,
        success: (res) => {
          const selectedSlot = availableSlots[res.tapIndex];
          this.confirmAppointment(doctor, selectedSlot);
        }
      });
    },

    // 确认预约
    confirmAppointment(doctor: Doctor, timeSlot: any) {
      const date = new Date(timeSlot.date);
      const dateStr = `${date.getMonth() + 1}月${date.getDate()}日`;
      const periodStr = timeSlot.period === 'morning' ? '上午' : '下午';

      wx.showModal({
        title: '确认预约',
        content: `医生：${doctor.name} ${doctor.title}\n科室：${doctor.department}\n时间：${dateStr}${periodStr} ${timeSlot.time}\n费用：¥${doctor.fee}`,
        success: (res) => {
          if (res.confirm) {
            this.createAppointment(doctor, timeSlot);
          }
        }
      });
    },

    // 创建预约
    createAppointment(doctor: Doctor, timeSlot: any) {
      // 创建预约记录
      const appointment = {
        id: Date.now().toString(),
        doctorId: doctor.id,
        doctorName: doctor.name,
        department: doctor.department,
        date: timeSlot.date,
        time: timeSlot.time,
        status: 'confirmed' as const,
        symptoms: '',
        fee: doctor.fee,
        location: `门诊2楼${doctor.department}`
      };

      try {
        // 保存到本地存储
        const existingAppointments = wx.getStorageSync('appointments') || [];
        existingAppointments.push(appointment);
        wx.setStorageSync('appointments', existingAppointments);

        // 设置就诊提醒
        reminderService.setAppointmentReminder(appointment);

        console.log('预约记录已保存:', appointment);

        wx.showToast({
          title: '预约成功',
          icon: 'success',
          success: () => {
            setTimeout(() => {
              wx.switchTab({
                url: '/pages/my-appointments/index'
              });
            }, 1500);
          }
        });
      } catch (error) {
        console.error('保存预约记录失败:', error);
        wx.showToast({
          title: '预约失败，请重试',
          icon: 'none'
        });
      }
    }
  }
});
