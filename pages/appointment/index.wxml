<!--appointment/index.wxml-->
<view class="page-container">
  <!-- 科室选择 -->
  <view class="department-section">
    <view class="section-title">选择科室</view>
    <t-grid column="3" class="department-grid">
      <t-grid-item 
        wx:for="{{departments}}" 
        wx:key="id"
        text="{{item.name}}" 
        icon="{{item.icon}}"
        bind:click="selectDepartment"
        data-department="{{item}}">
      </t-grid-item>
    </t-grid>
  </view>

  <!-- 医生列表 -->
  <view wx:if="{{selectedDepartment}}" class="doctors-section">
    <view class="section-title">{{selectedDepartment.name}}医生</view>
    <view class="doctors-list">
      <t-card 
        wx:for="{{filteredDoctors}}" 
        wx:key="id"
        class="doctor-card"
        bind:tap="selectDoctor"
        data-doctor="{{item}}">
        
        <view class="doctor-info">
          <image src="{{item.avatar}}" class="doctor-avatar"></image>
          <view class="doctor-details">
            <view class="doctor-header">
              <text class="doctor-name">{{item.name}}</text>
              <text class="doctor-title">{{item.title}}</text>
            </view>
            <view class="doctor-meta">
              <text class="doctor-experience">{{item.experience}}</text>
              <view class="doctor-rating">
                <text class="rating-text">⭐ {{item.rating}}</text>
              </view>
            </view>
            <view class="doctor-specialty">
              <text class="specialty-label">擅长：</text>
              <text class="specialty-text">{{item.specialty.join('、')}}</text>
            </view>
            <view class="doctor-fee">
              <text class="fee-label">挂号费：</text>
              <text class="fee-amount">¥{{item.fee}}</text>
            </view>
          </view>
        </view>

        <!-- 可预约时间 -->
        <view class="available-slots">
          <text class="slots-title">可预约时间：</text>
          <view class="slots-list">
            <view 
              wx:for="{{item.availableSlots}}" 
              wx:for-item="slot"
              wx:key="*this"
              class="slot-item {{slot.available ? 'available' : 'unavailable'}}">
              <text class="slot-date">{{slot.date}}</text>
              <text class="slot-time">{{slot.period === 'morning' ? '上午' : '下午'}} {{slot.time}}</text>
              <text class="slot-status">{{slot.available ? '可约' : '已满'}}</text>
            </view>
          </view>
        </view>
      </t-card>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{!selectedDepartment}}" class="empty-state">
    <image src="https://img.icons8.com/color/96/stethoscope.png" class="empty-icon"></image>
    <text class="empty-text">请先选择科室</text>
  </view>
</view>

<!-- Toast组件 -->
<t-toast id="t-toast" />
