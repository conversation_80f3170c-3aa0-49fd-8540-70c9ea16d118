/**my-appointments/index.wxss**/

/* 页面容器 */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 32rpx;
}

/* 统计区域 */
.stats-section {
  margin-bottom: 32rpx;
}

.stats-card {
  background: linear-gradient(135deg, #0052D9 0%, #0085FF 100%);
  color: white;
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
}

.stats-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 预约列表区域 */
.appointments-section {
  flex: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 48rpx;
}

.empty-button {
  width: 240rpx;
  height: 72rpx;
  border-radius: 36rpx;
}

/* 时间轴 */
.appointment-timeline {
  padding-left: 0;
}

.timeline-item {
  margin-bottom: 32rpx;
}

/* 预约卡片 */
.appointment-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-left: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.appointment-info {
  display: flex;
  flex-direction: column;
}

.doctor-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #000000;
  margin-bottom: 8rpx;
}

.department {
  font-size: 28rpx;
  color: #666666;
}

/* 预约详情 */
.appointment-details {
  margin-bottom: 24rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666666;
  width: 200rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 28rpx;
  color: #000000;
  flex: 1;
}

/* 操作按钮 */
.appointment-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}
