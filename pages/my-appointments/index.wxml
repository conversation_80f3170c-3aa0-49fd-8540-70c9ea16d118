<!--my-appointments/index.wxml-->
<view class="page-container">
  <!-- 预约统计 -->
  <view class="stats-section">
    <t-card class="stats-card">
      <view class="stats-content">
        <view class="stat-item">
          <text class="stat-number">{{appointmentStats.total}}</text>
          <text class="stat-label">总预约</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{appointmentStats.pending}}</text>
          <text class="stat-label">待就诊</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{appointmentStats.completed}}</text>
          <text class="stat-label">已完成</text>
        </view>
      </view>
    </t-card>
  </view>

  <!-- 预约列表 -->
  <view class="appointments-section">
    <view wx:if="{{appointments.length === 0}}" class="empty-state">
      <image src="https://img.icons8.com/color/96/calendar.png" class="empty-icon"></image>
      <text class="empty-text">暂无预约记录</text>
      <t-button 
        class="empty-button" 
        type="primary" 
        bind:tap="goToAppointment">
        立即预约
      </t-button>
    </view>

    <view wx:else>
      <t-timeline class="appointment-timeline">
        <t-timeline-item 
          wx:for="{{appointments}}" 
          wx:key="id"
          dot-color="{{item.status === 'confirmed' ? '#0052D9' : item.status === 'completed' ? '#00A870' : '#ED7B2F'}}"
          class="timeline-item">
          
          <t-card class="appointment-card" bind:tap="viewAppointmentDetail" data-appointment="{{item}}">
            <view class="appointment-header">
              <view class="appointment-info">
                <text class="doctor-name">{{item.doctorName}}</text>
                <text class="department">{{item.department}}</text>
              </view>
              <t-tag 
                variant="light" 
                theme="{{item.status === 'confirmed' ? 'primary' : item.status === 'completed' ? 'success' : 'warning'}}">
                {{item.status === 'confirmed' ? '已确认' : item.status === 'completed' ? '已完成' : '待确认'}}
              </t-tag>
            </view>
            
            <view class="appointment-details">
              <view class="detail-row">
                <text class="detail-label">📅 就诊时间</text>
                <text class="detail-value">{{item.date}} {{item.time}}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">📍 就诊地点</text>
                <text class="detail-value">{{item.location}}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">💰 挂号费用</text>
                <text class="detail-value">¥{{item.fee}}</text>
              </view>
              <view wx:if="{{item.symptoms}}" class="detail-row">
                <text class="detail-label">🏥 症状描述</text>
                <text class="detail-value">{{item.symptoms}}</text>
              </view>
            </view>

            <view wx:if="{{item.status === 'confirmed'}}" class="appointment-actions">
              <t-button 
                size="small" 
                variant="outline" 
                bind:tap="cancelAppointment"
                data-id="{{item.id}}"
                catch:tap="true">
                取消预约
              </t-button>
              <t-button 
                size="small" 
                type="primary"
                bind:tap="modifyAppointment"
                data-id="{{item.id}}"
                catch:tap="true">
                修改时间
              </t-button>
            </view>
          </t-card>
        </t-timeline-item>
      </t-timeline>
    </view>
  </view>
</view>

<!-- Toast组件 -->
<t-toast id="t-toast" />
