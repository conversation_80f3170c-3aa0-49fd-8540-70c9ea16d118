// my-appointments/index.ts
import { mockAppointments, Appointment } from '../../data/mock/index';
import { reminderService } from '../../services/reminder';

Component({
  data: {
    appointments: [] as Appointment[],
    appointmentStats: {
      total: 0,
      pending: 0,
      completed: 0
    }
  },

  lifetimes: {
    attached() {
      this.loadAppointments();
    }
  },

  pageLifetimes: {
    show() {
      // 页面显示时重新加载数据
      this.loadAppointments();
    }
  },

  methods: {
    // 加载预约数据
    loadAppointments() {
      try {
        // 从本地存储获取预约数据
        const storedAppointments = wx.getStorageSync('appointments') || [];
        // 合并模拟数据和存储数据
        const appointments = [...mockAppointments, ...storedAppointments];

        // 计算统计数据
        const stats = {
          total: appointments.length,
          pending: appointments.filter(a => a.status === 'confirmed').length,
          completed: appointments.filter(a => a.status === 'completed').length
        };

        this.setData({
          appointments,
          appointmentStats: stats
        });
      } catch (error) {
        console.error('加载预约数据失败:', error);
        // 使用模拟数据作为备选
        this.setData({
          appointments: mockAppointments,
          appointmentStats: {
            total: mockAppointments.length,
            pending: mockAppointments.filter(a => a.status === 'confirmed').length,
            completed: mockAppointments.filter(a => a.status === 'completed').length
          }
        });
      }
    },

    // 查看预约详情
    viewAppointmentDetail(e: any) {
      const appointment = e.currentTarget.dataset.appointment;
      wx.showModal({
        title: '预约详情',
        content: `医生：${appointment.doctorName}\n科室：${appointment.department}\n时间：${appointment.date} ${appointment.time}\n地点：${appointment.location}`,
        showCancel: false,
        confirmText: '知道了'
      });
    },

    // 取消预约
    cancelAppointment(e: any) {
      const appointmentId = e.currentTarget.dataset.id;

      wx.showModal({
        title: '确认取消',
        content: '确定要取消这个预约吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              // 从本地存储中删除预约
              const storedAppointments = wx.getStorageSync('appointments') || [];
              const updatedStoredAppointments = storedAppointments.filter((a: any) => a.id !== appointmentId);
              wx.setStorageSync('appointments', updatedStoredAppointments);

              // 取消对应的提醒
              reminderService.cancelReminder(appointmentId);

              // 更新页面数据
              const appointments = this.data.appointments.filter(a => a.id !== appointmentId);
              this.setData({
                appointments
              });

              wx.showToast({
                title: '预约已取消',
                icon: 'success'
              });

              // 重新计算统计数据
              this.updateStats();
            } catch (error) {
              console.error('取消预约失败:', error);
              wx.showToast({
                title: '取消失败，请重试',
                icon: 'none'
              });
            }
          }
        }
      });
    },

    // 修改预约
    modifyAppointment(e: any) {
      const appointmentId = e.currentTarget.dataset.id;

      wx.showToast({
        title: '修改功能开发中',
        icon: 'none'
      });
    },

    // 跳转到预约页面
    goToAppointment() {
      wx.navigateTo({
        url: '/pages/appointment/index'
      });
    },

    // 更新统计数据
    updateStats() {
      const { appointments } = this.data;
      const stats = {
        total: appointments.length,
        pending: appointments.filter(a => a.status === 'confirmed').length,
        completed: appointments.filter(a => a.status === 'completed').length
      };

      this.setData({
        appointmentStats: stats
      });
    }
  }
});
