{"compilerOptions": {"target": "ES2018", "lib": ["ES2018"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "typeRoots": ["./typings"], "types": ["miniprogram-api-typings"], "baseUrl": ".", "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.js", "typings/**/*.d.ts"], "exclude": ["node_modules"]}