// app.ts
import { reminderService } from './services/reminder';

App<IAppOption>({
  globalData: {},
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        console.log(res.code)
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      },
    })

    // 初始化提醒服务
    reminderService.startReminderCheck();

    // 清理过期提醒
    reminderService.cleanupExpiredReminders();

    console.log('智慧医院小程序启动完成');
  },

  onShow() {
    // 应用显示时重新启动提醒检查
    reminderService.startReminderCheck();
  },

  onHide() {
    // 应用隐藏时停止提醒检查（节省资源）
    reminderService.stopReminderCheck();
  }
})