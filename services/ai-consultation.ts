import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Doctor, symptomDepartmentMap, doctors, departments } from '../data/mock/index';
import { reminderService } from './reminder';

export interface ConsultationState {
  step: 'symptom' | 'analysis' | 'doctor' | 'time' | 'confirm' | 'reminder';
  symptoms: string[];
  recommendedDepartments: string[];
  selectedDoctor?: Doctor;
  selectedTime?: {
    date: string;
    period: string;
    time: string;
  };
  messages: ChatMessage[];
}

class AIConsultationService {
  private state: ConsultationState = {
    step: 'symptom',
    symptoms: [],
    recommendedDepartments: [],
    messages: []
  };

  // 初始化对话
  initConsultation(): ChatMessage {
    const welcomeMessage: ChatMessage = {
      id: this.generateId(),
      type: 'ai',
      content: '您好！我是AI医疗助手小智🤖\n\n请告诉我您哪里不舒服，我会为您推荐合适的科室和医生。',
      timestamp: Date.now(),
      options: ['发烧了', '头疼', '咳嗽', '腹痛', '外伤', '其他症状']
    };

    this.state.messages = [welcomeMessage];
    return welcomeMessage;
  }

  // 处理用户输入
  async processUserInput(input: string): Promise<ChatMessage[]> {
    const userMessage: ChatMessage = {
      id: this.generateId(),
      type: 'user',
      content: input,
      timestamp: Date.now()
    };

    this.state.messages.push(userMessage);

    let aiResponse: ChatMessage;

    switch (this.state.step) {
      case 'symptom':
        aiResponse = this.handleSymptomInput(input);
        break;
      case 'analysis':
        aiResponse = this.handleAnalysisConfirm(input);
        break;
      case 'doctor':
        aiResponse = this.handleDoctorSelection(input);
        break;
      case 'time':
        aiResponse = this.handleTimeSelection(input);
        break;
      case 'confirm':
        aiResponse = this.handleConfirmation(input);
        break;
      default:
        aiResponse = this.handleDefault();
    }

    this.state.messages.push(aiResponse);
    return [userMessage, aiResponse];
  }

  // 处理症状输入
  private handleSymptomInput(input: string): ChatMessage {
    this.state.symptoms.push(input);

    // 分析症状，推荐科室
    const recommendedDepts = this.analyzeSymptoms(input);
    this.state.recommendedDepartments = recommendedDepts;

    if (recommendedDepts.length > 0) {
      this.state.step = 'analysis';
      const deptNames = recommendedDepts.map(id => {
        const dept = departments.find(d => d.id === id);
        return dept ? dept.name : '';
      }).filter(Boolean);

      return {
        id: this.generateId(),
        type: 'ai',
        content: `根据您的症状"${input}"，我建议您挂${deptNames.join('或')}。\n\n${deptNames[0]}医生专门处理这类症状，通常能够有效诊治。\n\n需要我帮您预约吗？`,
        timestamp: Date.now(),
        options: ['是的，帮我预约', '我想了解更多', '换个科室']
      };
    } else {
      return {
        id: this.generateId(),
        type: 'ai',
        content: '您的症状比较特殊，建议您先挂内科进行初步检查，医生会根据情况为您转诊到合适的科室。\n\n需要我帮您预约内科吗？',
        timestamp: Date.now(),
        options: ['预约内科', '我再想想']
      };
    }
  }

  // 处理分析确认
  private handleAnalysisConfirm(input: string): ChatMessage {
    if (input.includes('预约') || input.includes('是的')) {
      this.state.step = 'doctor';
      return this.recommendDoctors();
    } else if (input.includes('了解更多')) {
      const deptId = this.state.recommendedDepartments[0];
      const dept = departments.find(d => d.id === deptId);
      return {
        id: this.generateId(),
        type: 'ai',
        content: `${dept?.name}主要诊治：${dept?.description}\n\n现在需要我帮您预约吗？`,
        timestamp: Date.now(),
        options: ['立即预约', '我再考虑考虑']
      };
    } else {
      return {
        id: this.generateId(),
        type: 'ai',
        content: '请告诉我您希望挂哪个科室，或者重新描述您的症状。',
        timestamp: Date.now(),
        options: ['内科', '外科', '儿科', '妇科', '重新描述症状']
      };
    }
  }

  // 推荐医生
  private recommendDoctors(): ChatMessage {
    const deptId = this.state.recommendedDepartments[0];
    const dept = departments.find(d => d.id === deptId);
    const availableDoctors = doctors.filter(d => d.department === dept?.name);

    if (availableDoctors.length > 0) {
      const topDoctor = availableDoctors.sort((a, b) => b.rating - a.rating)[0];
      this.state.selectedDoctor = topDoctor;

      return {
        id: this.generateId(),
        type: 'ai',
        content: `为您推荐${dept?.name}的${topDoctor.name}：\n\n👨‍⚕️ ${topDoctor.title}\n⭐ 患者评分：${topDoctor.rating}分\n💼 ${topDoctor.experience}\n🏥 擅长：${topDoctor.specialty.join('、')}\n💰 挂号费：${topDoctor.fee}元`,
        timestamp: Date.now(),
        options: [`选择${topDoctor.name}`, '看其他医生', '了解更多信息']
      };
    } else {
      return {
        id: this.generateId(),
        type: 'ai',
        content: '抱歉，该科室暂时没有可预约的医生，建议您选择其他科室或稍后再试。',
        timestamp: Date.now(),
        options: ['选择其他科室', '稍后再试']
      };
    }
  }

  // 处理医生选择
  private handleDoctorSelection(input: string): ChatMessage {
    if (input.includes('选择') && this.state.selectedDoctor) {
      this.state.step = 'time';
      return this.showAvailableTime();
    } else if (input.includes('其他医生')) {
      // 显示更多医生选项
      return this.showMoreDoctors();
    } else {
      return {
        id: this.generateId(),
        type: 'ai',
        content: '请选择您要预约的医生，或者告诉我您的具体需求。',
        timestamp: Date.now(),
        options: ['确认当前医生', '查看其他医生']
      };
    }
  }

  // 显示可预约时间
  private showAvailableTime(): ChatMessage {
    const doctor = this.state.selectedDoctor!;
    const availableSlots = doctor.availableSlots.filter(slot => slot.available);

    if (availableSlots.length > 0) {
      const timeOptions = availableSlots.map(slot => {
        const date = new Date(slot.date);
        const dateStr = `${date.getMonth() + 1}月${date.getDate()}日`;
        const periodStr = slot.period === 'morning' ? '上午' : '下午';
        return `${dateStr}${periodStr}(${slot.time})`;
      });

      return {
        id: this.generateId(),
        type: 'ai',
        content: `${doctor.name}的可预约时间：\n\n${timeOptions.map((time, index) => `${index + 1}. ${time} ✅可约`).join('\n')}\n\n请选择您方便的时间：`,
        timestamp: Date.now(),
        options: timeOptions
      };
    } else {
      return {
        id: this.generateId(),
        type: 'ai',
        content: `抱歉，${doctor.name}近期没有可预约的时间。建议您：`,
        timestamp: Date.now(),
        options: ['选择其他医生', '查看更多时间', '加入候补名单']
      };
    }
  }

  // 处理时间选择
  private handleTimeSelection(input: string): ChatMessage {
    // 解析用户选择的时间
    const doctor = this.state.selectedDoctor!;
    const selectedSlot = doctor.availableSlots.find(slot =>
      input.includes(slot.date.split('-')[2]) || input.includes(slot.time)
    );

    if (selectedSlot) {
      this.state.selectedTime = {
        date: selectedSlot.date,
        period: selectedSlot.period,
        time: selectedSlot.time
      };
      this.state.step = 'confirm';
      return this.showConfirmation();
    } else {
      return {
        id: this.generateId(),
        type: 'ai',
        content: '请选择一个具体的时间段，或者告诉我您的时间偏好。',
        timestamp: Date.now(),
        options: ['重新选择时间', '查看其他医生']
      };
    }
  }

  // 显示预约确认信息
  private showConfirmation(): ChatMessage {
    const doctor = this.state.selectedDoctor!;
    const time = this.state.selectedTime!;
    const date = new Date(time.date);
    const dateStr = `${date.getMonth() + 1}月${date.getDate()}日`;
    const periodStr = time.period === 'morning' ? '上午' : '下午';

    return {
      id: this.generateId(),
      type: 'ai',
      content: `📋 请确认预约信息：\n\n🏥 科室：${doctor.department}\n👨‍⚕️ 医生：${doctor.name} ${doctor.title}\n📅 时间：${dateStr}${periodStr} ${time.time}\n💰 费用：${doctor.fee}元\n📍 地点：门诊2楼${doctor.department}\n\n确认预约吗？`,
      timestamp: Date.now(),
      options: ['确认预约', '修改时间', '重新选择医生']
    };
  }

  // 处理最终确认
  private handleConfirmation(input: string): ChatMessage {
    if (input.includes('确认')) {
      // 创建预约记录
      this.createAppointmentRecord();

      this.state.step = 'reminder';
      return {
        id: this.generateId(),
        type: 'ai',
        content: `🎉 预约成功！\n\n温馨提醒：\n✅ 请携带身份证和医保卡\n✅ 提前15分钟到达医院\n✅ 如需空腹检查会提前通知您\n\n我已为您设置就诊提醒，到时会通知您。祝您身体健康！`,
        timestamp: Date.now(),
        options: ['查看预约详情', '返回首页', '继续咨询']
      };
    } else if (input.includes('修改')) {
      this.state.step = 'time';
      return this.showAvailableTime();
    } else {
      this.state.step = 'doctor';
      return this.recommendDoctors();
    }
  }

  // 创建预约记录
  private createAppointmentRecord() {
    if (!this.state.selectedDoctor || !this.state.selectedTime) {
      return;
    }

    const appointment = {
      id: Date.now().toString(),
      doctorId: this.state.selectedDoctor.id,
      doctorName: this.state.selectedDoctor.name,
      department: this.state.selectedDoctor.department,
      date: this.state.selectedTime.date,
      time: this.state.selectedTime.time,
      status: 'confirmed' as const,
      symptoms: this.state.symptoms.join('、'),
      fee: this.state.selectedDoctor.fee,
      location: `门诊2楼${this.state.selectedDoctor.department}`
    };

    // 保存到本地存储
    try {
      const existingAppointments = wx.getStorageSync('appointments') || [];
      existingAppointments.push(appointment);
      wx.setStorageSync('appointments', existingAppointments);

      // 设置就诊提醒
      reminderService.setAppointmentReminder(appointment);

      console.log('预约记录已保存:', appointment);
    } catch (error) {
      console.error('保存预约记录失败:', error);
    }
  }

  // 显示更多医生
  private showMoreDoctors(): ChatMessage {
    const deptId = this.state.recommendedDepartments[0];
    const dept = departments.find(d => d.id === deptId);
    const availableDoctors = doctors.filter(d => d.department === dept?.name);

    const doctorList = availableDoctors.map(doctor =>
      `👨‍⚕️ ${doctor.name} ${doctor.title}\n⭐ ${doctor.rating}分 | 💰 ${doctor.fee}元`
    ).join('\n\n');

    return {
      id: this.generateId(),
      type: 'ai',
      content: `${dept?.name}可预约医生：\n\n${doctorList}\n\n请选择您要预约的医生：`,
      timestamp: Date.now(),
      options: availableDoctors.map(d => d.name)
    };
  }

  // 分析症状，返回推荐科室ID
  private analyzeSymptoms(symptom: string): string[] {
    const recommendedDepts: string[] = [];

    for (const [keyword, deptNames] of Object.entries(symptomDepartmentMap)) {
      if (symptom.includes(keyword)) {
        deptNames.forEach(deptName => {
          const dept = departments.find(d => d.name === deptName);
          if (dept && !recommendedDepts.includes(dept.id)) {
            recommendedDepts.push(dept.id);
          }
        });
      }
    }

    // 如果没有匹配到，默认推荐内科
    if (recommendedDepts.length === 0) {
      const internalDept = departments.find(d => d.name === '内科');
      if (internalDept) {
        recommendedDepts.push(internalDept.id);
      }
    }

    return recommendedDepts;
  }

  // 默认处理
  private handleDefault(): ChatMessage {
    return {
      id: this.generateId(),
      type: 'ai',
      content: '抱歉，我没有理解您的意思。请重新描述您的症状，或者选择下面的选项：',
      timestamp: Date.now(),
      options: ['重新开始', '人工客服', '常见问题']
    };
  }

  // 生成唯一ID
  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  // 获取当前状态
  getState(): ConsultationState {
    return { ...this.state };
  }

  // 重置状态
  reset(): void {
    this.state = {
      step: 'symptom',
      symptoms: [],
      recommendedDepartments: [],
      messages: []
    };
  }
}

export const aiConsultationService = new AIConsultationService();
export default aiConsultationService;
