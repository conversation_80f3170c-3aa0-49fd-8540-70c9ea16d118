// 全局状态管理服务
import { Appointment, Doctor, Department } from '../data/mock/index';

interface AppState {
  user: {
    isLoggedIn: boolean;
    userInfo: any;
  };
  appointments: Appointment[];
  doctors: Doctor[];
  departments: Department[];
  loading: {
    appointments: boolean;
    doctors: boolean;
    aiConsultation: boolean;
  };
}

class StoreService {
  private state: AppState = {
    user: {
      isLoggedIn: false,
      userInfo: null
    },
    appointments: [],
    doctors: [],
    departments: [],
    loading: {
      appointments: false,
      doctors: false,
      aiConsultation: false
    }
  };

  private listeners: { [key: string]: Function[] } = {};

  // 获取状态
  getState(): AppState {
    return { ...this.state };
  }

  // 更新状态
  setState(updates: Partial<AppState>) {
    const oldState = { ...this.state };
    this.state = { ...this.state, ...updates };
    
    // 通知监听器
    Object.keys(updates).forEach(key => {
      if (this.listeners[key]) {
        this.listeners[key].forEach(listener => {
          listener(this.state[key as keyof AppState], oldState[key as keyof AppState]);
        });
      }
    });
  }

  // 订阅状态变化
  subscribe(key: keyof AppState, listener: Function) {
    if (!this.listeners[key]) {
      this.listeners[key] = [];
    }
    this.listeners[key].push(listener);

    // 返回取消订阅函数
    return () => {
      const index = this.listeners[key].indexOf(listener);
      if (index > -1) {
        this.listeners[key].splice(index, 1);
      }
    };
  }

  // 预约相关操作
  async loadAppointments() {
    this.setState({
      loading: { ...this.state.loading, appointments: true }
    });

    try {
      // 从本地存储加载预约数据
      const storedAppointments = wx.getStorageSync('appointments') || [];
      const mockAppointments = (await import('../data/mock/index')).mockAppointments;
      const appointments = [...mockAppointments, ...storedAppointments];

      this.setState({
        appointments,
        loading: { ...this.state.loading, appointments: false }
      });

      return appointments;
    } catch (error) {
      console.error('加载预约数据失败:', error);
      this.setState({
        loading: { ...this.state.loading, appointments: false }
      });
      throw error;
    }
  }

  async addAppointment(appointment: Appointment) {
    try {
      // 保存到本地存储
      const existingAppointments = wx.getStorageSync('appointments') || [];
      existingAppointments.push(appointment);
      wx.setStorageSync('appointments', existingAppointments);

      // 更新状态
      const appointments = [...this.state.appointments, appointment];
      this.setState({ appointments });

      return appointment;
    } catch (error) {
      console.error('添加预约失败:', error);
      throw error;
    }
  }

  async removeAppointment(appointmentId: string) {
    try {
      // 从本地存储删除
      const storedAppointments = wx.getStorageSync('appointments') || [];
      const updatedStoredAppointments = storedAppointments.filter((a: any) => a.id !== appointmentId);
      wx.setStorageSync('appointments', updatedStoredAppointments);

      // 更新状态
      const appointments = this.state.appointments.filter(a => a.id !== appointmentId);
      this.setState({ appointments });

      return true;
    } catch (error) {
      console.error('删除预约失败:', error);
      throw error;
    }
  }

  // 医生相关操作
  async loadDoctors() {
    this.setState({
      loading: { ...this.state.loading, doctors: true }
    });

    try {
      const { doctors } = await import('../data/mock/index');
      this.setState({
        doctors,
        loading: { ...this.state.loading, doctors: false }
      });

      return doctors;
    } catch (error) {
      console.error('加载医生数据失败:', error);
      this.setState({
        loading: { ...this.state.loading, doctors: false }
      });
      throw error;
    }
  }

  // 科室相关操作
  async loadDepartments() {
    try {
      const { departments } = await import('../data/mock/index');
      this.setState({ departments });
      return departments;
    } catch (error) {
      console.error('加载科室数据失败:', error);
      throw error;
    }
  }

  // 用户相关操作
  setUser(userInfo: any) {
    this.setState({
      user: {
        isLoggedIn: true,
        userInfo
      }
    });
  }

  logout() {
    this.setState({
      user: {
        isLoggedIn: false,
        userInfo: null
      }
    });
  }

  // 加载状态管理
  setLoading(key: keyof AppState['loading'], loading: boolean) {
    this.setState({
      loading: {
        ...this.state.loading,
        [key]: loading
      }
    });
  }

  // 获取预约统计
  getAppointmentStats() {
    const { appointments } = this.state;
    return {
      total: appointments.length,
      pending: appointments.filter(a => a.status === 'confirmed').length,
      completed: appointments.filter(a => a.status === 'completed').length,
      cancelled: appointments.filter(a => a.status === 'cancelled').length
    };
  }

  // 根据科室筛选医生
  getDoctorsByDepartment(departmentName: string) {
    return this.state.doctors.filter(doctor => doctor.department === departmentName);
  }

  // 获取医生详情
  getDoctorById(doctorId: string) {
    return this.state.doctors.find(doctor => doctor.id === doctorId);
  }

  // 清理数据
  clearData() {
    this.state = {
      user: {
        isLoggedIn: false,
        userInfo: null
      },
      appointments: [],
      doctors: [],
      departments: [],
      loading: {
        appointments: false,
        doctors: false,
        aiConsultation: false
      }
    };
  }
}

export const store = new StoreService();
export default store;
