// 就诊提醒服务
import { Appointment } from '../data/mock/index';

class ReminderService {
  // 设置就诊提醒
  setAppointmentReminder(appointment: Appointment) {
    try {
      // 计算提醒时间（就诊前1小时）
      const appointmentDateTime = new Date(`${appointment.date} ${appointment.time}`);
      const reminderTime = new Date(appointmentDateTime.getTime() - 60 * 60 * 1000); // 提前1小时
      
      // 检查是否已过期
      if (reminderTime.getTime() <= Date.now()) {
        console.log('预约时间已过期，不设置提醒');
        return false;
      }

      // 保存提醒到本地存储
      const reminders = wx.getStorageSync('reminders') || [];
      const reminder = {
        id: `reminder_${appointment.id}`,
        appointmentId: appointment.id,
        reminderTime: reminderTime.getTime(),
        title: '就诊提醒',
        content: `您预约的${appointment.doctorName}医生的门诊即将开始，请提前15分钟到达${appointment.location}`,
        isActive: true
      };

      // 检查是否已存在相同的提醒
      const existingIndex = reminders.findIndex((r: any) => r.appointmentId === appointment.id);
      if (existingIndex >= 0) {
        reminders[existingIndex] = reminder;
      } else {
        reminders.push(reminder);
      }

      wx.setStorageSync('reminders', reminders);
      
      // 启动提醒检查
      this.startReminderCheck();
      
      console.log('就诊提醒已设置:', reminder);
      return true;
    } catch (error) {
      console.error('设置提醒失败:', error);
      return false;
    }
  }

  // 启动提醒检查
  startReminderCheck() {
    // 清除之前的定时器
    if (this.reminderTimer) {
      clearInterval(this.reminderTimer);
    }

    // 每分钟检查一次提醒
    this.reminderTimer = setInterval(() => {
      this.checkReminders();
    }, 60000); // 60秒检查一次

    // 立即检查一次
    this.checkReminders();
  }

  private reminderTimer: any = null;

  // 检查提醒
  private checkReminders() {
    try {
      const reminders = wx.getStorageSync('reminders') || [];
      const now = Date.now();
      
      reminders.forEach((reminder: any) => {
        if (reminder.isActive && reminder.reminderTime <= now) {
          // 触发提醒
          this.triggerReminder(reminder);
          
          // 标记为已触发
          reminder.isActive = false;
        }
      });

      // 更新存储
      wx.setStorageSync('reminders', reminders);
    } catch (error) {
      console.error('检查提醒失败:', error);
    }
  }

  // 触发提醒
  private triggerReminder(reminder: any) {
    // 显示系统通知
    wx.showModal({
      title: reminder.title,
      content: reminder.content,
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        console.log('提醒已显示:', reminder);
      }
    });

    // 如果支持，可以发送本地通知
    if (wx.getSystemInfoSync().platform !== 'devtools') {
      wx.showToast({
        title: '就诊提醒',
        icon: 'none',
        duration: 3000
      });
    }
  }

  // 取消提醒
  cancelReminder(appointmentId: string) {
    try {
      const reminders = wx.getStorageSync('reminders') || [];
      const updatedReminders = reminders.filter((r: any) => r.appointmentId !== appointmentId);
      wx.setStorageSync('reminders', updatedReminders);
      
      console.log('提醒已取消:', appointmentId);
      return true;
    } catch (error) {
      console.error('取消提醒失败:', error);
      return false;
    }
  }

  // 获取所有活跃提醒
  getActiveReminders() {
    try {
      const reminders = wx.getStorageSync('reminders') || [];
      return reminders.filter((r: any) => r.isActive);
    } catch (error) {
      console.error('获取提醒失败:', error);
      return [];
    }
  }

  // 清理过期提醒
  cleanupExpiredReminders() {
    try {
      const reminders = wx.getStorageSync('reminders') || [];
      const now = Date.now();
      const validReminders = reminders.filter((r: any) => {
        // 保留未来的提醒和最近24小时内的已触发提醒
        return r.isActive || (now - r.reminderTime < 24 * 60 * 60 * 1000);
      });
      
      wx.setStorageSync('reminders', validReminders);
      console.log('过期提醒已清理');
    } catch (error) {
      console.error('清理提醒失败:', error);
    }
  }

  // 停止提醒检查
  stopReminderCheck() {
    if (this.reminderTimer) {
      clearInterval(this.reminderTimer);
      this.reminderTimer = null;
    }
  }
}

export const reminderService = new ReminderService();
export default reminderService;
