# 智慧医院 - AI智能问诊小程序

基于微信小程序和TDesign UI组件库开发的智能医院预约系统，以AI智能问诊为核心功能。

## 🚀 项目特色

- **AI智能问诊**：通过对话式交互，智能分析症状并推荐合适的科室和医生
- **便捷预约**：支持传统科室选择和AI推荐两种预约方式
- **智能提醒**：自动设置就诊提醒，避免错过预约时间
- **现代UI**：基于TDesign设计系统，提供优秀的用户体验

## 📱 功能模块

### 1. AI智能问诊
- 🤖 智能对话界面，模仿微信聊天体验
- 🔍 症状分析和科室推荐
- 👨‍⚕️ 医生推荐和介绍
- 📅 可预约时间展示
- ✅ 一键预约确认

### 2. 预约挂号
- 🏥 科室导航和医生列表
- ⭐ 医生评分和专长展示
- 🕐 实时可预约时间
- 💰 透明的费用显示

### 3. 我的预约
- 📋 预约记录管理
- 📊 预约统计概览
- ✏️ 预约修改和取消
- 🔔 就诊提醒设置

### 4. 智能提醒
- ⏰ 自动就诊提醒
- 📱 本地通知推送
- 🗓️ 提醒时间管理

## 🛠️ 技术栈

- **框架**：微信小程序原生开发
- **UI组件**：TDesign小程序版
- **语言**：TypeScript
- **状态管理**：自研轻量级Store
- **数据存储**：微信小程序本地存储

## 📁 项目结构

```
miniprogram/
├── app.ts                 # 应用入口
├── app.json              # 应用配置
├── app.wxss              # 全局样式
├── pages/                # 页面目录
│   ├── index/            # 首页
│   ├── ai-consultation/  # AI问诊页面
│   ├── appointment/      # 预约挂号页面
│   └── my-appointments/  # 我的预约页面
├── components/           # 自定义组件
├── services/            # 业务服务层
│   ├── ai-consultation.ts  # AI问诊服务
│   ├── reminder.ts         # 提醒服务
│   └── store.ts           # 状态管理
├── data/                # 数据层
│   └── mock/            # 模拟数据
├── utils/               # 工具函数
│   ├── api/             # API服务
│   └── util.ts          # 通用工具
├── assets/              # 静态资源
└── typings/             # 类型定义
```

## 🎯 核心功能实现

### AI问诊流程

1. **症状收集**：用户描述症状，AI进行初步分析
2. **智能分析**：基于症状关键词匹配推荐科室
3. **医生推荐**：展示科室内评分最高的医生
4. **时间选择**：显示医生的可预约时间段
5. **预约确认**：确认预约信息并创建预约记录
6. **就诊提醒**：自动设置提醒，避免错过就诊

### 数据管理

- **本地存储**：使用微信小程序Storage API存储预约数据
- **状态管理**：全局Store管理应用状态
- **数据同步**：页面间数据实时同步更新

### 用户体验优化

- **加载状态**：所有异步操作都有加载提示
- **错误处理**：完善的错误提示和重试机制
- **响应式设计**：适配不同尺寸的手机屏幕
- **无障碍访问**：支持语音朗读和高对比度

## 🚀 快速开始

### 环境要求

- 微信开发者工具
- Node.js 14+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 开发调试

1. 使用微信开发者工具打开项目
2. 在工具中预览和调试
3. 真机预览测试功能

### 构建部署

1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 提交审核和发布

## 📋 开发计划

### 已完成功能 ✅

- [x] 项目架构设计与TDesign集成
- [x] 首页布局开发
- [x] AI问诊对话界面开发
- [x] AI问诊流程逻辑实现
- [x] 预约挂号功能开发
- [x] 我的预约管理功能
- [x] 数据模拟与状态管理

### 后续优化 🔄

- [ ] 真实API接口集成
- [ ] 用户登录和身份验证
- [ ] 支付功能集成
- [ ] 医生在线咨询
- [ ] 检查报告查看
- [ ] 健康档案管理
- [ ] 消息推送优化
- [ ] 性能优化和缓存策略

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址：[GitHub Repository]
- 问题反馈：[Issues]
- 邮箱：<EMAIL>

---

**注意**：本项目目前使用模拟数据，实际部署时需要集成真实的医院管理系统API。
